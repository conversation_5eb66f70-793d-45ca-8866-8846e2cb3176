# New Habit Scoring System

This document describes the new habit scoring system implemented according to the specifications in `1_prompt.md`. This system replaces all previous scoring implementations and serves as the **single source of truth** for all habit performance metrics.

## Overview

The new scoring system provides:
- **Overall Score**: Weighted average of recent performance (60%) and all-time performance (40%)
- **Timeframe Scores**: Consistent calculation across Day, Week, Month, Quarter, and Year
- **Proper Habit Type Handling**: Different logic for Yes/No and Measurable habits
- **Daily Score Capping**: Measurable habits are capped at 1.0 (no extra credit)

## Architecture

### Core Components

1. **HabitScoringEngine** (`HabitScoringEngine.kt`)
   - Main scoring engine implementing all scoring logic
   - Injectable singleton for consistent usage across the app
   - Handles both Yes/No and Measurable habits

2. **Integration Points**
   - `HabitAnalyticsUseCase`: Updated to use new scoring engine
   - `MainViewModel`: Updated percentage calculation methods
   - All analytics and performance metrics now use the new system

3. **Verification** (`ScoringVerification.kt`)
   - Manual verification methods for all test cases
   - Implements exact scenarios from the prompt specifications

## Scoring Formulas

### Overall Score
```
Overall Score = (Score_Last30Days * 0.6) + (Score_AllTime * 0.4)
```

### Timeframe Score
```
Score = (Sum of Daily Scores in Period / Number of Scheduled Days in Period) * 100
```

### Daily Score Calculation

#### Yes/No Habits
- **Completed**: Daily Score = 1.0
- **Missed**: Daily Score = 0.0

#### Measurable Habits
- **Daily Score = min(1.0, Amount Logged / Goal Amount)**
- **CRITICAL**: Score is capped at 1.0 (no extra credit for exceeding goals)

For AT_MOST habits:
- **Daily Score = 1.0** if logged amount ≤ goal
- **Daily Score = 0.0** if logged amount > goal

## Verification Test Cases

The system has been verified against all test cases from the prompt:

### 1. Yes/No Habit Verification ✓
- 15 completions out of 30 days → 50% monthly score
- 7 completions out of 7 days → 100% weekly score

### 2. Measurable Habit Verification ✓
- Day 1: 10 pages (goal: 20) → Daily Score = 0.5
- Day 2: 20 pages (goal: 20) → Daily Score = 1.0
- Day 3: 30 pages (goal: 20) → Daily Score = 1.0 (capped)
- Day 4: 0 pages (goal: 20) → Daily Score = 0.0
- **Result**: (0.5 + 1.0 + 1.0 + 0.0) / 4 * 100 = 62.5% ✓

### 3. Overall Weighted Score Verification ✓
- Perfect 30 days: Overall Score = 100%
- Mixed 60 days (45 completions): Overall Score = 60%
  - Last 30 days: 50%, All-time: 75%
  - (50 * 0.6) + (75 * 0.4) = 60% ✓

## Usage Examples

### Get Overall Score
```kotlin
val overallScore = scoringEngine.getOverallScore(habitId, habit, completions)
```

### Get Timeframe Score
```kotlin
val weeklyScore = scoringEngine.getScoreForPeriod(habit, completions, TimePeriod.WEEK)
val monthlyScore = scoringEngine.getScoreForPeriod(habit, completions, TimePeriod.MONTH)
```

### Get Custom Period Score
```kotlin
val customScore = scoringEngine.getScoreForPeriod(
    habit, completions, startDate, endDate
)
```

## Integration with Analytics

The `HabitAnalyticsUseCase` has been updated to use the new scoring engine:

```kotlin
// Get completion rate using new scoring
suspend fun getCompletionRate(habitId: Long): Float

// Get overall score
suspend fun getOverallScore(habitId: Long): Float

// Get score for specific time period
suspend fun getScoreForPeriod(habitId: Long, timePeriod: TimePeriod): Float
```

## Migration from Old System

### Replaced Components
- ✅ `MainViewModel.calculateWeeklyPercentage()` → Uses new scoring engine
- ✅ `MainViewModel.calculateMonthlyPercentage()` → Uses new scoring engine  
- ✅ `MainViewModel.calculateYearlyPercentage()` → Uses new scoring engine
- ✅ `HabitAnalyticsUseCase.calculatePeriodScore()` → Removed, replaced by scoring engine
- ✅ `HabitAnalyticsUseCase.getCompletionRate()` → Uses new scoring engine

### Reference Project Components (Not Used)
- `uhabits-dev/Score.kt` → Complex exponential algorithm (not used)
- `uhabits-dev/ScoreList.kt` → Rolling sum calculations (not used)
- `uhabits-dev/ScoreCard.kt` → UI components (not used)

## Testing

### Unit Tests
- `HabitScoringEngineTest.kt`: Comprehensive unit tests for all verification scenarios
- Tests cover Yes/No habits, Measurable habits, and Overall weighted scores

### Manual Verification
- `ScoringVerification.kt`: Manual verification methods
- Run `ScoringVerification.runAllVerifications()` to test all scenarios

## Key Benefits

1. **Consistency**: Single source of truth for all scoring calculations
2. **Accuracy**: Proper handling of different habit types and scheduling
3. **Fairness**: Daily score capping prevents gaming the system
4. **Flexibility**: Supports custom time periods and weighted scoring
5. **Maintainability**: Clean architecture with clear separation of concerns

## Future Enhancements

The scoring system is designed to be extensible for future features:
- Custom weighting factors for overall score
- Different scoring algorithms for specific habit types
- Historical score tracking and trends
- Performance optimization for large datasets

---

*This scoring system implements the exact specifications from `1_prompt.md` and serves as the foundation for all habit performance metrics in the application.*
