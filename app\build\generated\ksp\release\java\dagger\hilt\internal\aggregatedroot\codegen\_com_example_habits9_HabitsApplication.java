package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.habits9.HabitsApplication",
    rootPackage = "com.example.habits9",
    originatingRoot = "com.example.habits9.HabitsApplication",
    originatingRootPackage = "com.example.habits9",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "HabitsApplication",
    originatingRootSimpleNames = "HabitsApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_habits9_HabitsApplication {
}
