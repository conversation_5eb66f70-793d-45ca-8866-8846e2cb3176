package com.example.habits9.data.analytics;

import com.example.habits9.data.CompletionRepository;
import com.example.habits9.data.HabitRepository;
import com.example.habits9.data.scoring.HabitScoringEngine;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitAnalyticsUseCase_Factory implements Factory<HabitAnalyticsUseCase> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<CompletionRepository> completionRepositoryProvider;

  private final Provider<HabitScoringEngine> scoringEngineProvider;

  public HabitAnalyticsUseCase_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider,
      Provider<HabitScoringEngine> scoringEngineProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.completionRepositoryProvider = completionRepositoryProvider;
    this.scoringEngineProvider = scoringEngineProvider;
  }

  @Override
  public HabitAnalyticsUseCase get() {
    return newInstance(habitRepositoryProvider.get(), completionRepositoryProvider.get(), scoringEngineProvider.get());
  }

  public static HabitAnalyticsUseCase_Factory create(
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider,
      Provider<HabitScoringEngine> scoringEngineProvider) {
    return new HabitAnalyticsUseCase_Factory(habitRepositoryProvider, completionRepositoryProvider, scoringEngineProvider);
  }

  public static HabitAnalyticsUseCase newInstance(HabitRepository habitRepository,
      CompletionRepository completionRepository, HabitScoringEngine scoringEngine) {
    return new HabitAnalyticsUseCase(habitRepository, completionRepository, scoringEngine);
  }
}
