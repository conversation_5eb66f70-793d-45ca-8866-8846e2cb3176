  Activity android.app  Application android.app  
AuthScreen android.app.Activity  Build android.app.Activity  CreateMeasurableHabitScreen android.app.Activity  CreateYesNoHabitScreen android.app.Activity  FirebaseAuth android.app.Activity  HabitDetailsScreen android.app.Activity  HabitReorderScreen android.app.Activity  HabitTypeSelectionScreen android.app.Activity  
HomeScreen android.app.Activity  ManageSectionsScreen android.app.Activity  NavHost android.app.Activity  SettingsScreen android.app.Activity  UHabits_99Theme android.app.Activity  VerificationScreen android.app.Activity  
composable android.app.Activity  onCreate android.app.Activity  rememberNavController android.app.Activity  
setContent android.app.Activity  toLongOrNull android.app.Activity  Context android.content  
AuthScreen android.content.Context  Build android.content.Context  CreateMeasurableHabitScreen android.content.Context  CreateYesNoHabitScreen android.content.Context  FirebaseAuth android.content.Context  HabitDetailsScreen android.content.Context  HabitReorderScreen android.content.Context  HabitTypeSelectionScreen android.content.Context  
HomeScreen android.content.Context  ManageSectionsScreen android.content.Context  NavHost android.content.Context  SettingsScreen android.content.Context  UHabits_99Theme android.content.Context  VerificationScreen android.content.Context  
composable android.content.Context  	dataStore android.content.Context  rememberNavController android.content.Context  
setContent android.content.Context  toLongOrNull android.content.Context  
AuthScreen android.content.ContextWrapper  Build android.content.ContextWrapper  CreateMeasurableHabitScreen android.content.ContextWrapper  CreateYesNoHabitScreen android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  HabitDetailsScreen android.content.ContextWrapper  HabitReorderScreen android.content.ContextWrapper  HabitTypeSelectionScreen android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  ManageSectionsScreen android.content.ContextWrapper  NavHost android.content.ContextWrapper  SettingsScreen android.content.ContextWrapper  UHabits_99Theme android.content.ContextWrapper  VerificationScreen android.content.ContextWrapper  
composable android.content.ContextWrapper  rememberNavController android.content.ContextWrapper  
setContent android.content.ContextWrapper  toLongOrNull android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  
AuthScreen  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  CreateMeasurableHabitScreen  android.view.ContextThemeWrapper  CreateYesNoHabitScreen  android.view.ContextThemeWrapper  FirebaseAuth  android.view.ContextThemeWrapper  HabitDetailsScreen  android.view.ContextThemeWrapper  HabitReorderScreen  android.view.ContextThemeWrapper  HabitTypeSelectionScreen  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  ManageSectionsScreen  android.view.ContextThemeWrapper  NavHost  android.view.ContextThemeWrapper  SettingsScreen  android.view.ContextThemeWrapper  UHabits_99Theme  android.view.ContextThemeWrapper  VerificationScreen  android.view.ContextThemeWrapper  
composable  android.view.ContextThemeWrapper  rememberNavController  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  toLongOrNull  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
AuthScreen #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  CreateMeasurableHabitScreen #androidx.activity.ComponentActivity  CreateYesNoHabitScreen #androidx.activity.ComponentActivity  FirebaseAuth #androidx.activity.ComponentActivity  HabitDetailsScreen #androidx.activity.ComponentActivity  HabitReorderScreen #androidx.activity.ComponentActivity  HabitTypeSelectionScreen #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  ManageSectionsScreen #androidx.activity.ComponentActivity  NavHost #androidx.activity.ComponentActivity  RequiresApi #androidx.activity.ComponentActivity  SettingsScreen #androidx.activity.ComponentActivity  UHabits_99Theme #androidx.activity.ComponentActivity  VerificationScreen #androidx.activity.ComponentActivity  
composable #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  rememberNavController #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  toLongOrNull #androidx.activity.ComponentActivity  
AuthScreen -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  CreateMeasurableHabitScreen -androidx.activity.ComponentActivity.Companion  CreateYesNoHabitScreen -androidx.activity.ComponentActivity.Companion  FirebaseAuth -androidx.activity.ComponentActivity.Companion  HabitDetailsScreen -androidx.activity.ComponentActivity.Companion  HabitReorderScreen -androidx.activity.ComponentActivity.Companion  HabitTypeSelectionScreen -androidx.activity.ComponentActivity.Companion  
HomeScreen -androidx.activity.ComponentActivity.Companion  ManageSectionsScreen -androidx.activity.ComponentActivity.Companion  NavHost -androidx.activity.ComponentActivity.Companion  SettingsScreen -androidx.activity.ComponentActivity.Companion  UHabits_99Theme -androidx.activity.ComponentActivity.Companion  VerificationScreen -androidx.activity.ComponentActivity.Companion  
composable -androidx.activity.ComponentActivity.Companion  rememberNavController -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  toLongOrNull -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  RequiresApi androidx.annotation  AnimatedContentScope androidx.compose.animation  animateColorAsState androidx.compose.animation  animateContentSize androidx.compose.animation  
AuthScreen /androidx.compose.animation.AnimatedContentScope  CreateMeasurableHabitScreen /androidx.compose.animation.AnimatedContentScope  CreateYesNoHabitScreen /androidx.compose.animation.AnimatedContentScope  HabitDetailsScreen /androidx.compose.animation.AnimatedContentScope  HabitReorderScreen /androidx.compose.animation.AnimatedContentScope  HabitTypeSelectionScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  ManageSectionsScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  VerificationScreen /androidx.compose.animation.AnimatedContentScope  toLongOrNull /androidx.compose.animation.AnimatedContentScope  Easing androidx.compose.animation.core  FastOutSlowInEasing androidx.compose.animation.core  Spring androidx.compose.animation.core  
SpringSpec androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  animateDpAsState androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  animateOffsetAsState androidx.compose.animation.core  spring androidx.compose.animation.core  tween androidx.compose.animation.core  DampingRatioNoBouncy &androidx.compose.animation.core.Spring  
StiffnessHigh &androidx.compose.animation.core.Spring  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  horizontalScroll androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures   detectDragGesturesAfterLongPress $androidx.compose.foundation.gestures  
AccentPrimary "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  BackgroundDark "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  BorderStroke "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CreateHabitViewModel "androidx.compose.foundation.layout  DarkBackground "androidx.compose.foundation.layout  DayChip "androidx.compose.foundation.layout  	DayOfWeek "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  DividerColor "androidx.compose.foundation.layout  Double "androidx.compose.foundation.layout  DropdownMenu "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  EnhancedFrequency "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
FrequencyType "androidx.compose.foundation.layout  FrequencyTypeTab "androidx.compose.foundation.layout  Habit "androidx.compose.foundation.layout  HabitReorderItem "androidx.compose.foundation.layout  HabitReorderViewModel "androidx.compose.foundation.layout  HabitSection "androidx.compose.foundation.layout  
HabitSortType "androidx.compose.foundation.layout  HapticFeedbackType "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  
LazyListState "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  	LocalTime "androidx.compose.foundation.layout  ManageSectionsViewModel "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MonthlyOptions "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  
ReminderState "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  
SectionColors "androidx.compose.foundation.layout  SectionListItem "androidx.compose.foundation.layout  Set "androidx.compose.foundation.layout  SortMenuItem "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Spring "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SurfaceVariantDark "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  SwitchDefaults "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextPrimary "androidx.compose.foundation.layout  
TextSecondary "androidx.compose.foundation.layout  
TimePicker "androidx.compose.foundation.layout  TimePickerDefaults "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  
WeeklyOptions "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  alpha "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  animateFloatAsState "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  findSectionIndex "androidx.compose.foundation.layout  focusRequester "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  indexOfFirst "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  	lowercase "androidx.compose.foundation.layout  minus "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  pointerInput "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  rememberTimePickerState "androidx.compose.foundation.layout  replaceFirstChar "androidx.compose.foundation.layout  setOf "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shadow "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  toDoubleOrNull "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  toSet "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  	uppercase "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  zIndex "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  End .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceAround .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AccentPrimary +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  BackgroundDark +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  CheckCircle +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  CompletionIndicator +androidx.compose.foundation.layout.BoxScope  DarkBackground +androidx.compose.foundation.layout.BoxScope  Divider +androidx.compose.foundation.layout.BoxScope  DividerColor +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  Email +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  HabitReorderItem +androidx.compose.foundation.layout.BoxScope  
HabitSortType +androidx.compose.foundation.layout.BoxScope  HapticFeedbackType +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  	Indicator +androidx.compose.foundation.layout.BoxScope  KeyboardOptions +androidx.compose.foundation.layout.BoxScope  KeyboardType +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  Lock +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MoreVert +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  OutlinedButton +androidx.compose.foundation.layout.BoxScope  OutlinedTextField +androidx.compose.foundation.layout.BoxScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  PasswordVisualTransformation +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  SectionListItem +androidx.compose.foundation.layout.BoxScope  Sort +androidx.compose.foundation.layout.BoxScope  SortMenuItem +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  SurfaceVariantDark +androidx.compose.foundation.layout.BoxScope  Tab +androidx.compose.foundation.layout.BoxScope  TabRow +androidx.compose.foundation.layout.BoxScope  TabRowDefaults +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  
TextButton +androidx.compose.foundation.layout.BoxScope  TextPrimary +androidx.compose.foundation.layout.BoxScope  
TextSecondary +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  animateFloatAsState +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  colors +androidx.compose.foundation.layout.BoxScope  com +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  
drawBehind +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  find +androidx.compose.foundation.layout.BoxScope  findSectionIndex +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  getValue +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  horizontalScroll +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  
isNullOrEmpty +androidx.compose.foundation.layout.BoxScope  isWeekStartDay +androidx.compose.foundation.layout.BoxScope  itemsIndexed +androidx.compose.foundation.layout.BoxScope  kotlinx +androidx.compose.foundation.layout.BoxScope  launch +androidx.compose.foundation.layout.BoxScope  	lowercase +androidx.compose.foundation.layout.BoxScope  outlinedButtonColors +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  
plusAssign +androidx.compose.foundation.layout.BoxScope  provideDelegate +androidx.compose.foundation.layout.BoxScope  replaceFirstChar +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  tabIndicatorOffset +androidx.compose.foundation.layout.BoxScope  take +androidx.compose.foundation.layout.BoxScope  toDoubleOrNull +androidx.compose.foundation.layout.BoxScope  tween +androidx.compose.foundation.layout.BoxScope  updateEmail +androidx.compose.foundation.layout.BoxScope  updatePassword +androidx.compose.foundation.layout.BoxScope  	uppercase +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  
AccentPrimary .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  BackgroundDark .androidx.compose.foundation.layout.ColumnScope  BorderStroke .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CheckCircle .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DarkBackground .androidx.compose.foundation.layout.ColumnScope  DayChip .androidx.compose.foundation.layout.ColumnScope  	DayOfWeek .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  DividerColor .androidx.compose.foundation.layout.ColumnScope  
DragIndicator .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  Email .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  FirebaseAuth .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
FrequencyType .androidx.compose.foundation.layout.ColumnScope  FrequencyTypeTab .androidx.compose.foundation.layout.ColumnScope  FrozenPaneLayout .androidx.compose.foundation.layout.ColumnScope  
HabitSortType .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  	Indicator .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  
MetricView .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MonthlyOptions .androidx.compose.foundation.layout.ColumnScope  Offset .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  OverviewSection .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RadioButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Role .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  
SectionColors .androidx.compose.foundation.layout.ColumnScope  SortMenuItem .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  SubHeaderRow .androidx.compose.foundation.layout.ColumnScope  SurfaceVariantDark .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  SwitchDefaults .androidx.compose.foundation.layout.ColumnScope  Tab .androidx.compose.foundation.layout.ColumnScope  TabRow .androidx.compose.foundation.layout.ColumnScope  TabRowDefaults .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TextPrimary .androidx.compose.foundation.layout.ColumnScope  
TextSecondary .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  
WeeklyOptions .androidx.compose.foundation.layout.ColumnScope  alpha .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  com .androidx.compose.foundation.layout.ColumnScope   detectDragGesturesAfterLongPress .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  
drawBehind .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  focusRequester .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  horizontalScroll .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  isWeekStartDay .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  itemsIndexed .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  minus .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  pointerInput .androidx.compose.foundation.layout.ColumnScope  
selectable .androidx.compose.foundation.layout.ColumnScope  selectableGroup .androidx.compose.foundation.layout.ColumnScope  setOf .androidx.compose.foundation.layout.ColumnScope  shadow .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  tabIndicatorOffset .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  toDoubleOrNull .androidx.compose.foundation.layout.ColumnScope  toSet .androidx.compose.foundation.layout.ColumnScope  updateEmail .androidx.compose.foundation.layout.ColumnScope  updatePassword .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  zIndex .androidx.compose.foundation.layout.ColumnScope  
AccentPrimary +androidx.compose.foundation.layout.RowScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  BackgroundDark +androidx.compose.foundation.layout.RowScope  BorderStroke +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  CompletionIndicator +androidx.compose.foundation.layout.RowScope  DarkBackground +androidx.compose.foundation.layout.RowScope  	DayOfWeek +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  DividerColor +androidx.compose.foundation.layout.RowScope  
DragIndicator +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FontFamily +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  
FrequencyType +androidx.compose.foundation.layout.RowScope  FrequencyTypeTab +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  
MetricView +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  Offset +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  RadioButtonDefaults +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  SortMenuButton +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  SurfaceVariantDark +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  SwitchDefaults +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextPrimary +androidx.compose.foundation.layout.RowScope  
TextSecondary +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  alpha +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  com +androidx.compose.foundation.layout.RowScope   detectDragGesturesAfterLongPress +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
drawBehind +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  horizontalScroll +androidx.compose.foundation.layout.RowScope  ifEmpty +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  
isNullOrEmpty +androidx.compose.foundation.layout.RowScope  isWeekStartDay +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  minus +androidx.compose.foundation.layout.RowScope  outlinedButtonColors +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  plus +androidx.compose.foundation.layout.RowScope  pointerInput +androidx.compose.foundation.layout.RowScope  setOf +androidx.compose.foundation.layout.RowScope  shadow +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  to +androidx.compose.foundation.layout.RowScope  toDoubleOrNull +androidx.compose.foundation.layout.RowScope  toSet +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  
foundation 3androidx.compose.foundation.layout.androidx.compose  lazy >androidx.compose.foundation.layout.androidx.compose.foundation  LazyListItemInfo Candroidx.compose.foundation.layout.androidx.compose.foundation.lazy  example &androidx.compose.foundation.layout.com  habits9 .androidx.compose.foundation.layout.com.example  data 6androidx.compose.foundation.layout.com.example.habits9  HabitSection ;androidx.compose.foundation.layout.com.example.habits9.data  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  LazyListItemInfo  androidx.compose.foundation.lazy  LazyListLayoutInfo  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  itemsIndexed  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  
AccentPrimary .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  CircleShape .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  CompletionIndicator .androidx.compose.foundation.lazy.LazyItemScope  DayChip .androidx.compose.foundation.lazy.LazyItemScope  DividerColor .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  HabitReorderItem .androidx.compose.foundation.lazy.LazyItemScope  HapticFeedbackType .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Offset .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  SectionListItem .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  SurfaceVariantDark .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TextPrimary .androidx.compose.foundation.lazy.LazyItemScope  
TextSecondary .androidx.compose.foundation.lazy.LazyItemScope  android .androidx.compose.foundation.lazy.LazyItemScope  animateFloatAsState .androidx.compose.foundation.lazy.LazyItemScope  
background .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  com .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  
drawBehind .androidx.compose.foundation.lazy.LazyItemScope  find .androidx.compose.foundation.lazy.LazyItemScope  findSectionIndex .androidx.compose.foundation.lazy.LazyItemScope  forEachIndexed .androidx.compose.foundation.lazy.LazyItemScope  getValue .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  horizontalScroll .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  
isNullOrEmpty .androidx.compose.foundation.lazy.LazyItemScope  kotlinx .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  minus .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  plus .androidx.compose.foundation.lazy.LazyItemScope  
plusAssign .androidx.compose.foundation.lazy.LazyItemScope  provideDelegate .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  toDoubleOrNull .androidx.compose.foundation.lazy.LazyItemScope  tween .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  index 1androidx.compose.foundation.lazy.LazyListItemInfo  offset 1androidx.compose.foundation.lazy.LazyListItemInfo  size 1androidx.compose.foundation.lazy.LazyListItemInfo  visibleItemsInfo 3androidx.compose.foundation.lazy.LazyListLayoutInfo  
AccentPrimary .androidx.compose.foundation.lazy.LazyListScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  CircleShape .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  CompletionIndicator .androidx.compose.foundation.lazy.LazyListScope  DayChip .androidx.compose.foundation.lazy.LazyListScope  	DayOfWeek .androidx.compose.foundation.lazy.LazyListScope  DividerColor .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  HabitReorderItem .androidx.compose.foundation.lazy.LazyListScope  HapticFeedbackType .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Offset .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  
SectionColors .androidx.compose.foundation.lazy.LazyListScope  SectionListItem .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  SurfaceVariantDark .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TextPrimary .androidx.compose.foundation.lazy.LazyListScope  
TextSecondary .androidx.compose.foundation.lazy.LazyListScope  android .androidx.compose.foundation.lazy.LazyListScope  animateFloatAsState .androidx.compose.foundation.lazy.LazyListScope  
background .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  com .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  
drawBehind .androidx.compose.foundation.lazy.LazyListScope  find .androidx.compose.foundation.lazy.LazyListScope  findSectionIndex .androidx.compose.foundation.lazy.LazyListScope  forEachIndexed .androidx.compose.foundation.lazy.LazyListScope  getValue .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  horizontalScroll .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  
isNullOrEmpty .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  itemsIndexed .androidx.compose.foundation.lazy.LazyListScope  kotlinx .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  minus .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  plus .androidx.compose.foundation.lazy.LazyListScope  
plusAssign .androidx.compose.foundation.lazy.LazyListScope  provideDelegate .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  toDoubleOrNull .androidx.compose.foundation.lazy.LazyListScope  tween .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  
layoutInfo .androidx.compose.foundation.lazy.LazyListState  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  toDoubleOrNull 4androidx.compose.foundation.text.KeyboardActionScope  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  CheckCircle ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  
DragIndicator ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Email ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  Sort ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  
DragHandle &androidx.compose.material.icons.filled  
DragIndicator &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  NotificationsOff &androidx.compose.material.icons.filled  Sort &androidx.compose.material.icons.filled  
AccentPrimary androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  BackgroundDark androidx.compose.material3  Boolean androidx.compose.material3  BorderStroke androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  CreateHabitViewModel androidx.compose.material3  DarkBackground androidx.compose.material3  DayChip androidx.compose.material3  	DayOfWeek androidx.compose.material3  Divider androidx.compose.material3  DividerColor androidx.compose.material3  Double androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  EnhancedFrequency androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  Float androidx.compose.material3  
FontWeight androidx.compose.material3  
FrequencyType androidx.compose.material3  FrequencyTypeTab androidx.compose.material3  Habit androidx.compose.material3  HabitReorderItem androidx.compose.material3  HabitReorderViewModel androidx.compose.material3  HabitSection androidx.compose.material3  
HabitSortType androidx.compose.material3  HapticFeedbackType androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  	ImeAction androidx.compose.material3  Int androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  
LazyListState androidx.compose.material3  LazyRow androidx.compose.material3  List androidx.compose.material3  	LocalTime androidx.compose.material3  ManageSectionsViewModel androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  MonthlyOptions androidx.compose.material3  Offset androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  
PaddingValues androidx.compose.material3  RadioButton androidx.compose.material3  RadioButtonColors androidx.compose.material3  RadioButtonDefaults androidx.compose.material3  
ReminderState androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  
SectionColors androidx.compose.material3  SectionListItem androidx.compose.material3  Set androidx.compose.material3  SortMenuItem androidx.compose.material3  Spacer androidx.compose.material3  Spring androidx.compose.material3  String androidx.compose.material3  SurfaceVariantDark androidx.compose.material3  Switch androidx.compose.material3  SwitchColors androidx.compose.material3  SwitchDefaults androidx.compose.material3  Tab androidx.compose.material3  TabPosition androidx.compose.material3  TabRow androidx.compose.material3  TabRowDefaults androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  TextPrimary androidx.compose.material3  
TextSecondary androidx.compose.material3  
TimePicker androidx.compose.material3  TimePickerColors androidx.compose.material3  TimePickerDefaults androidx.compose.material3  TimePickerState androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TrailingIcon androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  
WeeklyOptions androidx.compose.material3  align androidx.compose.material3  alpha androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  animateFloatAsState androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  find androidx.compose.material3  findSectionIndex androidx.compose.material3  focusRequester androidx.compose.material3  forEach androidx.compose.material3  format androidx.compose.material3  getValue androidx.compose.material3  
graphicsLayer androidx.compose.material3  height androidx.compose.material3  indexOfFirst androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  kotlinx androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  	lowercase androidx.compose.material3  minus androidx.compose.material3  mutableStateOf androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  
plusAssign androidx.compose.material3  pointerInput androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  rememberTimePickerState androidx.compose.material3  replaceFirstChar androidx.compose.material3  setOf androidx.compose.material3  setValue androidx.compose.material3  shadow androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  take androidx.compose.material3  takeIf androidx.compose.material3  to androidx.compose.material3  toDoubleOrNull androidx.compose.material3  toIntOrNull androidx.compose.material3  toList androidx.compose.material3  toSet androidx.compose.material3  topAppBarColors androidx.compose.material3  tween androidx.compose.material3  	uppercase androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  zIndex androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  onBackground &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  
AccentPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DividerColor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextFieldDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  SurfaceVariantDark 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TextPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
TextSecondary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
background 6androidx.compose.material3.ExposedDropdownMenuBoxScope  colors 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  colors .androidx.compose.material3.RadioButtonDefaults  colors )androidx.compose.material3.SwitchDefaults  	Indicator )androidx.compose.material3.TabRowDefaults  tabIndicatorOffset )androidx.compose.material3.TabRowDefaults  colors -androidx.compose.material3.TimePickerDefaults  hour *androidx.compose.material3.TimePickerState  minute *androidx.compose.material3.TimePickerState  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  
foundation +androidx.compose.material3.androidx.compose  lazy 6androidx.compose.material3.androidx.compose.foundation  LazyListItemInfo ;androidx.compose.material3.androidx.compose.foundation.lazy  example androidx.compose.material3.com  habits9 &androidx.compose.material3.com.example  data .androidx.compose.material3.com.example.habits9  HabitSection 3androidx.compose.material3.com.example.habits9.data  
AccentPrimary androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Anchor androidx.compose.runtime  Arrangement androidx.compose.runtime  BackgroundDark androidx.compose.runtime  Boolean androidx.compose.runtime  BorderStroke androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  CreateHabitViewModel androidx.compose.runtime  DarkBackground androidx.compose.runtime  DayChip androidx.compose.runtime  	DayOfWeek androidx.compose.runtime  Divider androidx.compose.runtime  DividerColor androidx.compose.runtime  Double androidx.compose.runtime  DropdownMenu androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  EnhancedFrequency androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  Float androidx.compose.runtime  
FontWeight androidx.compose.runtime  
FrequencyType androidx.compose.runtime  FrequencyTypeTab androidx.compose.runtime  Habit androidx.compose.runtime  HabitReorderItem androidx.compose.runtime  HabitReorderViewModel androidx.compose.runtime  HabitSection androidx.compose.runtime  
HabitSortType androidx.compose.runtime  HapticFeedbackType androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  	ImeAction androidx.compose.runtime  Int androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  
LazyListState androidx.compose.runtime  LazyRow androidx.compose.runtime  List androidx.compose.runtime  	LocalTime androidx.compose.runtime  ManageSectionsViewModel androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MonthlyOptions androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
ReminderState androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  
SectionColors androidx.compose.runtime  SectionListItem androidx.compose.runtime  Set androidx.compose.runtime  SortMenuItem androidx.compose.runtime  Spacer androidx.compose.runtime  Spring androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  SurfaceVariantDark androidx.compose.runtime  Switch androidx.compose.runtime  SwitchDefaults androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TextPrimary androidx.compose.runtime  
TextSecondary androidx.compose.runtime  
TimePicker androidx.compose.runtime  TimePickerDefaults androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TrailingIcon androidx.compose.runtime  Unit androidx.compose.runtime  
WeeklyOptions androidx.compose.runtime  align androidx.compose.runtime  alpha androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  animateFloatAsState androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  find androidx.compose.runtime  findSectionIndex androidx.compose.runtime  focusRequester androidx.compose.runtime  forEach androidx.compose.runtime  format androidx.compose.runtime  getValue androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  height androidx.compose.runtime  indexOfFirst androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  kotlinx androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  	lowercase androidx.compose.runtime  minus androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  
plusAssign androidx.compose.runtime  pointerInput androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  rememberTimePickerState androidx.compose.runtime  replaceFirstChar androidx.compose.runtime  setOf androidx.compose.runtime  setValue androidx.compose.runtime  shadow androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  take androidx.compose.runtime  takeIf androidx.compose.runtime  to androidx.compose.runtime  toDoubleOrNull androidx.compose.runtime  toIntOrNull androidx.compose.runtime  toList androidx.compose.runtime  toSet androidx.compose.runtime  topAppBarColors androidx.compose.runtime  tween androidx.compose.runtime  	uppercase androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  zIndex androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue (androidx.compose.runtime.MutableIntState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  
foundation )androidx.compose.runtime.androidx.compose  lazy 4androidx.compose.runtime.androidx.compose.foundation  LazyListItemInfo 9androidx.compose.runtime.androidx.compose.foundation.lazy  example androidx.compose.runtime.com  habits9 $androidx.compose.runtime.com.example  data ,androidx.compose.runtime.com.example.habits9  HabitSection 1androidx.compose.runtime.com.example.habits9.data  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  ComposableFunction3 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  zIndex androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
drawBehind androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  focusRequester androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  horizontalScroll androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  
selectable androidx.compose.ui.Modifier  selectableGroup androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  tabIndicatorOffset androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  zIndex androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  alpha &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  horizontalScroll &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  selectableGroup &androidx.compose.ui.Modifier.Companion  shadow &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  tabIndicatorOffset &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  
drawBehind androidx.compose.ui.draw  scale androidx.compose.ui.draw  shadow androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  requestFocus (androidx.compose.ui.focus.FocusRequester  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  plus #androidx.compose.ui.geometry.Offset  
plusAssign #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  height !androidx.compose.ui.geometry.Size  minDimension !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  
compositeOver androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  
compositeOver "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  red "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  dp /androidx.compose.ui.graphics.GraphicsLayerScope  scaleX /androidx.compose.ui.graphics.GraphicsLayerScope  scaleY /androidx.compose.ui.graphics.GraphicsLayerScope  toPx /androidx.compose.ui.graphics.GraphicsLayerScope  translationX /androidx.compose.ui.graphics.GraphicsLayerScope  translationY /androidx.compose.ui.graphics.GraphicsLayerScope  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  
AccentPrimary 0androidx.compose.ui.graphics.drawscope.DrawScope  DividerColor 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  
TextSecondary 0androidx.compose.ui.graphics.drawscope.DrawScope  center 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedback "androidx.compose.ui.hapticfeedback  HapticFeedbackType "androidx.compose.ui.hapticfeedback  performHapticFeedback 1androidx.compose.ui.hapticfeedback.HapticFeedback  	Companion 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  TextHandleMove 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  TextHandleMove ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  consume 4androidx.compose.ui.input.pointer.PointerInputChange   detectDragGesturesAfterLongPress 3androidx.compose.ui.input.pointer.PointerInputScope  LocalContext androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  SoftwareKeyboardController androidx.compose.ui.platform  hide 7androidx.compose.ui.platform.SoftwareKeyboardController  Role androidx.compose.ui.semantics  	Companion "androidx.compose.ui.semantics.Role  RadioButton "androidx.compose.ui.semantics.Role  RadioButton ,androidx.compose.ui.semantics.Role.Companion  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Done (androidx.compose.ui.text.input.ImeAction  Done 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Decimal +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Decimal 5androidx.compose.ui.text.input.KeyboardType.Companion  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  End (androidx.compose.ui.text.style.TextAlign  Start (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  End 2androidx.compose.ui.text.style.TextAlign.Companion  Start 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toPx  androidx.compose.ui.unit.Density  plus androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  
AuthScreen #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CreateMeasurableHabitScreen #androidx.core.app.ComponentActivity  CreateYesNoHabitScreen #androidx.core.app.ComponentActivity  FirebaseAuth #androidx.core.app.ComponentActivity  HabitDetailsScreen #androidx.core.app.ComponentActivity  HabitReorderScreen #androidx.core.app.ComponentActivity  HabitTypeSelectionScreen #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  ManageSectionsScreen #androidx.core.app.ComponentActivity  NavHost #androidx.core.app.ComponentActivity  RequiresApi #androidx.core.app.ComponentActivity  SettingsScreen #androidx.core.app.ComponentActivity  UHabits_99Theme #androidx.core.app.ComponentActivity  VerificationScreen #androidx.core.app.ComponentActivity  
composable #androidx.core.app.ComponentActivity  rememberNavController #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  toLongOrNull #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  
AuthScreen #androidx.navigation.NavGraphBuilder  CreateMeasurableHabitScreen #androidx.navigation.NavGraphBuilder  CreateYesNoHabitScreen #androidx.navigation.NavGraphBuilder  HabitDetailsScreen #androidx.navigation.NavGraphBuilder  HabitReorderScreen #androidx.navigation.NavGraphBuilder  HabitTypeSelectionScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  ManageSectionsScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  VerificationScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  toLongOrNull #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Application com.example.habits9  HabitsApplication com.example.habits9  HiltAndroidApp com.example.habits9  AT_LEAST com.example.habits9.data  AT_MOST com.example.habits9.data  
AccentPrimary com.example.habits9.data  AlertDialog com.example.habits9.data  	Alignment com.example.habits9.data  ApplicationContext com.example.habits9.data  Arrangement com.example.habits9.data  BackgroundDark com.example.habits9.data  Boolean com.example.habits9.data  Box com.example.habits9.data  COMPLETIONS_COLLECTION com.example.habits9.data  Column com.example.habits9.data  
Completion com.example.habits9.data  CompletionRepository com.example.habits9.data  
Composable com.example.habits9.data  Context com.example.habits9.data  DAILY com.example.habits9.data  	DataStore com.example.habits9.data  DatabaseFrequency com.example.habits9.data  DayChip com.example.habits9.data  	DayOfWeek com.example.habits9.data  DividerColor com.example.habits9.data  Double com.example.habits9.data  EnhancedFrequency com.example.habits9.data  	Exception com.example.habits9.data  FirebaseAuth com.example.habits9.data  FirebaseFirestore com.example.habits9.data  FirestoreCompletion com.example.habits9.data  FirestoreConverters com.example.habits9.data  FirestoreHabit com.example.habits9.data  FirestoreHabitSection com.example.habits9.data  Flow com.example.habits9.data  
FontWeight com.example.habits9.data  
FrequencyType com.example.habits9.data  FrequencyTypeTab com.example.habits9.data  HABITS_COLLECTION com.example.habits9.data  HABIT_SECTIONS_COLLECTION com.example.habits9.data  Habit com.example.habits9.data  HabitFrequency com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionRepository com.example.habits9.data  
HabitSortType com.example.habits9.data  	HabitType com.example.habits9.data  IllegalArgumentException com.example.habits9.data  IllegalStateException com.example.habits9.data  Inject com.example.habits9.data  Int com.example.habits9.data  LazyRow com.example.habits9.data  List com.example.habits9.data  ListenerRegistration com.example.habits9.data  Log com.example.habits9.data  Long com.example.habits9.data  MONTHLY com.example.habits9.data  Map com.example.habits9.data  
MaterialTheme com.example.habits9.data  Modifier com.example.habits9.data  MonthlyOptions com.example.habits9.data  	NUMERICAL com.example.habits9.data  NoSuchElementException com.example.habits9.data  NumericalHabitType com.example.habits9.data  OutlinedTextField com.example.habits9.data  OutlinedTextFieldDefaults com.example.habits9.data  Preferences com.example.habits9.data  PreferencesKeys com.example.habits9.data  Row com.example.habits9.data  Set com.example.habits9.data  	Singleton com.example.habits9.data  Spacer com.example.habits9.data  String com.example.habits9.data  System com.example.habits9.data  TAG com.example.habits9.data  Text com.example.habits9.data  
TextButton com.example.habits9.data  TextPrimary com.example.habits9.data  
TextSecondary com.example.habits9.data  USERS_COLLECTION com.example.habits9.data  UUID com.example.habits9.data  Unit com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  WEEKLY com.example.habits9.data  
WeeklyOptions com.example.habits9.data  YES_NO com.example.habits9.data  all com.example.habits9.data  any com.example.habits9.data  auth com.example.habits9.data  await com.example.habits9.data  
awaitClose com.example.habits9.data  callbackFlow com.example.habits9.data  close com.example.habits9.data  colors com.example.habits9.data  completionToFirestore com.example.habits9.data  contains com.example.habits9.data  	dataStore com.example.habits9.data  edit com.example.habits9.data  	emptyList com.example.habits9.data  fillMaxWidth com.example.habits9.data  filter com.example.habits9.data  find com.example.habits9.data  findHabitDocumentId com.example.habits9.data  	firestore com.example.habits9.data  firestoreToCompletion com.example.habits9.data  firestoreToHabit com.example.habits9.data  firestoreToHabitSection com.example.habits9.data  first com.example.habits9.data  firstNotNullOfOrNull com.example.habits9.data  flatten com.example.habits9.data  forEach com.example.habits9.data  fromInt com.example.habits9.data  
fromString com.example.habits9.data  	fromValue com.example.habits9.data  getValue com.example.habits9.data  habitSectionToFirestore com.example.habits9.data  height com.example.habits9.data  isBlank com.example.habits9.data  
isNotBlank com.example.habits9.data  
isNullOrBlank com.example.habits9.data  java com.example.habits9.data  joinToString com.example.habits9.data  kotlin com.example.habits9.data  let com.example.habits9.data  listOf com.example.habits9.data  	lowercase com.example.habits9.data  map com.example.habits9.data  
mapNotNull com.example.habits9.data  mapOf com.example.habits9.data  minus com.example.habits9.data  
mutableListOf com.example.habits9.data  mutableMapOf com.example.habits9.data  mutableStateOf com.example.habits9.data  padding com.example.habits9.data  plus com.example.habits9.data  provideDelegate com.example.habits9.data  remember com.example.habits9.data  replace com.example.habits9.data  replaceFirstChar com.example.habits9.data  set com.example.habits9.data  setOf com.example.habits9.data  setValue com.example.habits9.data  size com.example.habits9.data  spacedBy com.example.habits9.data  split com.example.habits9.data  stringPreferencesKey com.example.habits9.data  take com.example.habits9.data  to com.example.habits9.data  toEnhancedFrequency com.example.habits9.data  toIntOrNull com.example.habits9.data  toLegacyFrequency com.example.habits9.data  toList com.example.habits9.data  toSet com.example.habits9.data  trySend com.example.habits9.data  	uppercase com.example.habits9.data  values com.example.habits9.data  weight com.example.habits9.data  width com.example.habits9.data  copy #com.example.habits9.data.Completion  habitId #com.example.habits9.data.Completion  id #com.example.habits9.data.Completion  	timestamp #com.example.habits9.data.Completion  value #com.example.habits9.data.Completion  COMPLETIONS_COLLECTION -com.example.habits9.data.CompletionRepository  
Completion -com.example.habits9.data.CompletionRepository  	Exception -com.example.habits9.data.CompletionRepository  FirebaseAuth -com.example.habits9.data.CompletionRepository  FirebaseFirestore -com.example.habits9.data.CompletionRepository  FirestoreCompletion -com.example.habits9.data.CompletionRepository  FirestoreConverters -com.example.habits9.data.CompletionRepository  Flow -com.example.habits9.data.CompletionRepository  IllegalStateException -com.example.habits9.data.CompletionRepository  Inject -com.example.habits9.data.CompletionRepository  List -com.example.habits9.data.CompletionRepository  ListenerRegistration -com.example.habits9.data.CompletionRepository  Log -com.example.habits9.data.CompletionRepository  Long -com.example.habits9.data.CompletionRepository  String -com.example.habits9.data.CompletionRepository  TAG -com.example.habits9.data.CompletionRepository  USERS_COLLECTION -com.example.habits9.data.CompletionRepository  auth -com.example.habits9.data.CompletionRepository  await -com.example.habits9.data.CompletionRepository  
awaitClose -com.example.habits9.data.CompletionRepository  callbackFlow -com.example.habits9.data.CompletionRepository  close -com.example.habits9.data.CompletionRepository  completionToFirestore -com.example.habits9.data.CompletionRepository  deleteCompletion -com.example.habits9.data.CompletionRepository  	emptyList -com.example.habits9.data.CompletionRepository  findHabitDocumentId -com.example.habits9.data.CompletionRepository  	firestore -com.example.habits9.data.CompletionRepository  firestoreToCompletion -com.example.habits9.data.CompletionRepository  first -com.example.habits9.data.CompletionRepository  flatten -com.example.habits9.data.CompletionRepository  getCompletionForHabitAndDate -com.example.habits9.data.CompletionRepository  getCompletionsForHabit -com.example.habits9.data.CompletionRepository  getCompletionsForHabitsInRange -com.example.habits9.data.CompletionRepository  habitIdToDocumentIdCache -com.example.habits9.data.CompletionRepository  insertCompletion -com.example.habits9.data.CompletionRepository  java -com.example.habits9.data.CompletionRepository  kotlin -com.example.habits9.data.CompletionRepository  launch -com.example.habits9.data.CompletionRepository  let -com.example.habits9.data.CompletionRepository  
mapNotNull -com.example.habits9.data.CompletionRepository  
mutableListOf -com.example.habits9.data.CompletionRepository  mutableMapOf -com.example.habits9.data.CompletionRepository  set -com.example.habits9.data.CompletionRepository  trySend -com.example.habits9.data.CompletionRepository  updateCompletion -com.example.habits9.data.CompletionRepository  COMPLETIONS_COLLECTION 7com.example.habits9.data.CompletionRepository.Companion  FirestoreCompletion 7com.example.habits9.data.CompletionRepository.Companion  FirestoreConverters 7com.example.habits9.data.CompletionRepository.Companion  IllegalStateException 7com.example.habits9.data.CompletionRepository.Companion  Log 7com.example.habits9.data.CompletionRepository.Companion  TAG 7com.example.habits9.data.CompletionRepository.Companion  USERS_COLLECTION 7com.example.habits9.data.CompletionRepository.Companion  auth 7com.example.habits9.data.CompletionRepository.Companion  await 7com.example.habits9.data.CompletionRepository.Companion  
awaitClose 7com.example.habits9.data.CompletionRepository.Companion  callbackFlow 7com.example.habits9.data.CompletionRepository.Companion  close 7com.example.habits9.data.CompletionRepository.Companion  completionToFirestore 7com.example.habits9.data.CompletionRepository.Companion  	emptyList 7com.example.habits9.data.CompletionRepository.Companion  findHabitDocumentId 7com.example.habits9.data.CompletionRepository.Companion  	firestore 7com.example.habits9.data.CompletionRepository.Companion  firestoreToCompletion 7com.example.habits9.data.CompletionRepository.Companion  first 7com.example.habits9.data.CompletionRepository.Companion  flatten 7com.example.habits9.data.CompletionRepository.Companion  java 7com.example.habits9.data.CompletionRepository.Companion  kotlin 7com.example.habits9.data.CompletionRepository.Companion  launch 7com.example.habits9.data.CompletionRepository.Companion  let 7com.example.habits9.data.CompletionRepository.Companion  
mapNotNull 7com.example.habits9.data.CompletionRepository.Companion  
mutableListOf 7com.example.habits9.data.CompletionRepository.Companion  mutableMapOf 7com.example.habits9.data.CompletionRepository.Companion  set 7com.example.habits9.data.CompletionRepository.Companion  trySend 7com.example.habits9.data.CompletionRepository.Companion  
dayOfMonth *com.example.habits9.data.DatabaseFrequency  dayOfWeekInMonth *com.example.habits9.data.DatabaseFrequency  
daysOfWeek *com.example.habits9.data.DatabaseFrequency  
frequencyType *com.example.habits9.data.DatabaseFrequency  repeatsEvery *com.example.habits9.data.DatabaseFrequency  weekOfMonth *com.example.habits9.data.DatabaseFrequency  	Companion "com.example.habits9.data.DayOfWeek  	DayOfWeek "com.example.habits9.data.DayOfWeek  FRIDAY "com.example.habits9.data.DayOfWeek  Int "com.example.habits9.data.DayOfWeek  List "com.example.habits9.data.DayOfWeek  MONDAY "com.example.habits9.data.DayOfWeek  SATURDAY "com.example.habits9.data.DayOfWeek  SUNDAY "com.example.habits9.data.DayOfWeek  String "com.example.habits9.data.DayOfWeek  THURSDAY "com.example.habits9.data.DayOfWeek  TUESDAY "com.example.habits9.data.DayOfWeek  	WEDNESDAY "com.example.habits9.data.DayOfWeek  	emptyList "com.example.habits9.data.DayOfWeek  find "com.example.habits9.data.DayOfWeek  	fromValue "com.example.habits9.data.DayOfWeek  fullName "com.example.habits9.data.DayOfWeek  
isNullOrBlank "com.example.habits9.data.DayOfWeek  joinToString "com.example.habits9.data.DayOfWeek  map "com.example.habits9.data.DayOfWeek  
mapNotNull "com.example.habits9.data.DayOfWeek  	shortName "com.example.habits9.data.DayOfWeek  split "com.example.habits9.data.DayOfWeek  toIntOrNull "com.example.habits9.data.DayOfWeek  value "com.example.habits9.data.DayOfWeek  values "com.example.habits9.data.DayOfWeek  	emptyList ,com.example.habits9.data.DayOfWeek.Companion  find ,com.example.habits9.data.DayOfWeek.Companion  	fromValue ,com.example.habits9.data.DayOfWeek.Companion  
isNullOrBlank ,com.example.habits9.data.DayOfWeek.Companion  joinToString ,com.example.habits9.data.DayOfWeek.Companion  map ,com.example.habits9.data.DayOfWeek.Companion  
mapNotNull ,com.example.habits9.data.DayOfWeek.Companion  split ,com.example.habits9.data.DayOfWeek.Companion  toIntOrNull ,com.example.habits9.data.DayOfWeek.Companion  values ,com.example.habits9.data.DayOfWeek.Companion  Boolean *com.example.habits9.data.EnhancedFrequency  	Companion *com.example.habits9.data.EnhancedFrequency  DAILY *com.example.habits9.data.EnhancedFrequency  DatabaseFrequency *com.example.habits9.data.EnhancedFrequency  	DayOfWeek *com.example.habits9.data.EnhancedFrequency  EnhancedFrequency *com.example.habits9.data.EnhancedFrequency  
FrequencyType *com.example.habits9.data.EnhancedFrequency  HabitFrequency *com.example.habits9.data.EnhancedFrequency  Int *com.example.habits9.data.EnhancedFrequency  List *com.example.habits9.data.EnhancedFrequency  Long *com.example.habits9.data.EnhancedFrequency  String *com.example.habits9.data.EnhancedFrequency  all *com.example.habits9.data.EnhancedFrequency  any *com.example.habits9.data.EnhancedFrequency  contains *com.example.habits9.data.EnhancedFrequency  
dayOfMonth *com.example.habits9.data.EnhancedFrequency  dayOfWeekInMonth *com.example.habits9.data.EnhancedFrequency  
daysOfWeek *com.example.habits9.data.EnhancedFrequency  	emptyList *com.example.habits9.data.EnhancedFrequency  first *com.example.habits9.data.EnhancedFrequency  fromDatabaseValues *com.example.habits9.data.EnhancedFrequency  
fromString *com.example.habits9.data.EnhancedFrequency  	fromValue *com.example.habits9.data.EnhancedFrequency  getOrdinalSuffix *com.example.habits9.data.EnhancedFrequency  getValidationError *com.example.habits9.data.EnhancedFrequency  isValid *com.example.habits9.data.EnhancedFrequency  joinToString *com.example.habits9.data.EnhancedFrequency  let *com.example.habits9.data.EnhancedFrequency  listOf *com.example.habits9.data.EnhancedFrequency  map *com.example.habits9.data.EnhancedFrequency  
mapNotNull *com.example.habits9.data.EnhancedFrequency  repeatsEvery *com.example.habits9.data.EnhancedFrequency  toDatabaseValues *com.example.habits9.data.EnhancedFrequency  toDisplayString *com.example.habits9.data.EnhancedFrequency  type *com.example.habits9.data.EnhancedFrequency  weekOfMonth *com.example.habits9.data.EnhancedFrequency  DAILY 4com.example.habits9.data.EnhancedFrequency.Companion  DatabaseFrequency 4com.example.habits9.data.EnhancedFrequency.Companion  	DayOfWeek 4com.example.habits9.data.EnhancedFrequency.Companion  EnhancedFrequency 4com.example.habits9.data.EnhancedFrequency.Companion  
FrequencyType 4com.example.habits9.data.EnhancedFrequency.Companion  all 4com.example.habits9.data.EnhancedFrequency.Companion  any 4com.example.habits9.data.EnhancedFrequency.Companion  contains 4com.example.habits9.data.EnhancedFrequency.Companion  	emptyList 4com.example.habits9.data.EnhancedFrequency.Companion  first 4com.example.habits9.data.EnhancedFrequency.Companion  fromDatabaseValues 4com.example.habits9.data.EnhancedFrequency.Companion  
fromString 4com.example.habits9.data.EnhancedFrequency.Companion  	fromValue 4com.example.habits9.data.EnhancedFrequency.Companion  joinToString 4com.example.habits9.data.EnhancedFrequency.Companion  let 4com.example.habits9.data.EnhancedFrequency.Companion  listOf 4com.example.habits9.data.EnhancedFrequency.Companion  map 4com.example.habits9.data.EnhancedFrequency.Companion  
mapNotNull 4com.example.habits9.data.EnhancedFrequency.Companion  	Companion &com.example.habits9.data.FrequencyType  DAILY &com.example.habits9.data.FrequencyType  
FrequencyType &com.example.habits9.data.FrequencyType  MONTHLY &com.example.habits9.data.FrequencyType  String &com.example.habits9.data.FrequencyType  WEEKLY &com.example.habits9.data.FrequencyType  
fromString &com.example.habits9.data.FrequencyType  name &com.example.habits9.data.FrequencyType  value &com.example.habits9.data.FrequencyType  values &com.example.habits9.data.FrequencyType  DAILY 0com.example.habits9.data.FrequencyType.Companion  MONTHLY 0com.example.habits9.data.FrequencyType.Companion  WEEKLY 0com.example.habits9.data.FrequencyType.Companion  
fromString 0com.example.habits9.data.FrequencyType.Companion  
FrequencyType com.example.habits9.data.Habit  	HabitType com.example.habits9.data.Habit  NumericalHabitType com.example.habits9.data.Habit  color com.example.habits9.data.Habit  completionDatesJson com.example.habits9.data.Habit  copy com.example.habits9.data.Habit  creationDate com.example.habits9.data.Habit  
currentStreak com.example.habits9.data.Habit  customOrderIndex com.example.habits9.data.Habit  
dayOfMonth com.example.habits9.data.Habit  dayOfWeekInMonth com.example.habits9.data.Habit  
daysOfWeek com.example.habits9.data.Habit  description com.example.habits9.data.Habit  
frequencyType com.example.habits9.data.Habit  frequencyTypeEnum com.example.habits9.data.Habit  fromInt com.example.habits9.data.Habit  
fromString com.example.habits9.data.Habit  	habitType com.example.habits9.data.Habit  id com.example.habits9.data.Habit  
isArchived com.example.habits9.data.Habit  name com.example.habits9.data.Habit  numericalHabitType com.example.habits9.data.Habit  position com.example.habits9.data.Habit  repeatsEvery com.example.habits9.data.Habit  	sectionId com.example.habits9.data.Habit  
targetType com.example.habits9.data.Habit  targetValue com.example.habits9.data.Habit  type com.example.habits9.data.Habit  unit com.example.habits9.data.Habit  uuid com.example.habits9.data.Habit  weekOfMonth com.example.habits9.data.Habit  Boolean (com.example.habits9.data.HabitRepository  	Exception (com.example.habits9.data.HabitRepository  FirebaseAuth (com.example.habits9.data.HabitRepository  FirebaseFirestore (com.example.habits9.data.HabitRepository  FirestoreConverters (com.example.habits9.data.HabitRepository  FirestoreHabit (com.example.habits9.data.HabitRepository  Flow (com.example.habits9.data.HabitRepository  HABITS_COLLECTION (com.example.habits9.data.HabitRepository  Habit (com.example.habits9.data.HabitRepository  IllegalStateException (com.example.habits9.data.HabitRepository  Inject (com.example.habits9.data.HabitRepository  Int (com.example.habits9.data.HabitRepository  List (com.example.habits9.data.HabitRepository  ListenerRegistration (com.example.habits9.data.HabitRepository  Log (com.example.habits9.data.HabitRepository  Long (com.example.habits9.data.HabitRepository  Map (com.example.habits9.data.HabitRepository  NoSuchElementException (com.example.habits9.data.HabitRepository  String (com.example.habits9.data.HabitRepository  TAG (com.example.habits9.data.HabitRepository  USERS_COLLECTION (com.example.habits9.data.HabitRepository  auth (com.example.habits9.data.HabitRepository  await (com.example.habits9.data.HabitRepository  
awaitClose (com.example.habits9.data.HabitRepository  callbackFlow (com.example.habits9.data.HabitRepository  	emptyList (com.example.habits9.data.HabitRepository  	firestore (com.example.habits9.data.HabitRepository  firestoreToHabit (com.example.habits9.data.HabitRepository  firstNotNullOfOrNull (com.example.habits9.data.HabitRepository  getAllHabits (com.example.habits9.data.HabitRepository  getAllHabitsOnce (com.example.habits9.data.HabitRepository  getHabitById (com.example.habits9.data.HabitRepository  getHabitByIdSync (com.example.habits9.data.HabitRepository  insertHabit (com.example.habits9.data.HabitRepository  java (com.example.habits9.data.HabitRepository  let (com.example.habits9.data.HabitRepository  
mapNotNull (com.example.habits9.data.HabitRepository  mapOf (com.example.habits9.data.HabitRepository  to (com.example.habits9.data.HabitRepository  updateCustomOrderIndices (com.example.habits9.data.HabitRepository  FirestoreConverters 2com.example.habits9.data.HabitRepository.Companion  FirestoreHabit 2com.example.habits9.data.HabitRepository.Companion  HABITS_COLLECTION 2com.example.habits9.data.HabitRepository.Companion  IllegalStateException 2com.example.habits9.data.HabitRepository.Companion  Log 2com.example.habits9.data.HabitRepository.Companion  NoSuchElementException 2com.example.habits9.data.HabitRepository.Companion  TAG 2com.example.habits9.data.HabitRepository.Companion  USERS_COLLECTION 2com.example.habits9.data.HabitRepository.Companion  auth 2com.example.habits9.data.HabitRepository.Companion  await 2com.example.habits9.data.HabitRepository.Companion  
awaitClose 2com.example.habits9.data.HabitRepository.Companion  callbackFlow 2com.example.habits9.data.HabitRepository.Companion  	emptyList 2com.example.habits9.data.HabitRepository.Companion  	firestore 2com.example.habits9.data.HabitRepository.Companion  firestoreToHabit 2com.example.habits9.data.HabitRepository.Companion  firstNotNullOfOrNull 2com.example.habits9.data.HabitRepository.Companion  java 2com.example.habits9.data.HabitRepository.Companion  let 2com.example.habits9.data.HabitRepository.Companion  
mapNotNull 2com.example.habits9.data.HabitRepository.Companion  mapOf 2com.example.habits9.data.HabitRepository.Companion  to 2com.example.habits9.data.HabitRepository.Companion  color %com.example.habits9.data.HabitSection  copy %com.example.habits9.data.HabitSection  displayOrder %com.example.habits9.data.HabitSection  id %com.example.habits9.data.HabitSection  let %com.example.habits9.data.HabitSection  name %com.example.habits9.data.HabitSection  	Exception /com.example.habits9.data.HabitSectionRepository  FirebaseAuth /com.example.habits9.data.HabitSectionRepository  FirebaseFirestore /com.example.habits9.data.HabitSectionRepository  FirestoreConverters /com.example.habits9.data.HabitSectionRepository  FirestoreHabitSection /com.example.habits9.data.HabitSectionRepository  Flow /com.example.habits9.data.HabitSectionRepository  HABIT_SECTIONS_COLLECTION /com.example.habits9.data.HabitSectionRepository  HabitSection /com.example.habits9.data.HabitSectionRepository  IllegalStateException /com.example.habits9.data.HabitSectionRepository  Inject /com.example.habits9.data.HabitSectionRepository  List /com.example.habits9.data.HabitSectionRepository  ListenerRegistration /com.example.habits9.data.HabitSectionRepository  Log /com.example.habits9.data.HabitSectionRepository  TAG /com.example.habits9.data.HabitSectionRepository  USERS_COLLECTION /com.example.habits9.data.HabitSectionRepository  auth /com.example.habits9.data.HabitSectionRepository  await /com.example.habits9.data.HabitSectionRepository  
awaitClose /com.example.habits9.data.HabitSectionRepository  callbackFlow /com.example.habits9.data.HabitSectionRepository  deleteHabitSection /com.example.habits9.data.HabitSectionRepository  	emptyList /com.example.habits9.data.HabitSectionRepository  	firestore /com.example.habits9.data.HabitSectionRepository  firestoreToHabitSection /com.example.habits9.data.HabitSectionRepository  getAllHabitSections /com.example.habits9.data.HabitSectionRepository  getAllSectionsSync /com.example.habits9.data.HabitSectionRepository  habitSectionToFirestore /com.example.habits9.data.HabitSectionRepository  insertHabitSection /com.example.habits9.data.HabitSectionRepository  java /com.example.habits9.data.HabitSectionRepository  let /com.example.habits9.data.HabitSectionRepository  
mapNotNull /com.example.habits9.data.HabitSectionRepository  updateHabitSection /com.example.habits9.data.HabitSectionRepository  updateHabitSections /com.example.habits9.data.HabitSectionRepository  FirestoreConverters 9com.example.habits9.data.HabitSectionRepository.Companion  FirestoreHabitSection 9com.example.habits9.data.HabitSectionRepository.Companion  HABIT_SECTIONS_COLLECTION 9com.example.habits9.data.HabitSectionRepository.Companion  IllegalStateException 9com.example.habits9.data.HabitSectionRepository.Companion  Log 9com.example.habits9.data.HabitSectionRepository.Companion  TAG 9com.example.habits9.data.HabitSectionRepository.Companion  USERS_COLLECTION 9com.example.habits9.data.HabitSectionRepository.Companion  auth 9com.example.habits9.data.HabitSectionRepository.Companion  await 9com.example.habits9.data.HabitSectionRepository.Companion  
awaitClose 9com.example.habits9.data.HabitSectionRepository.Companion  callbackFlow 9com.example.habits9.data.HabitSectionRepository.Companion  	emptyList 9com.example.habits9.data.HabitSectionRepository.Companion  	firestore 9com.example.habits9.data.HabitSectionRepository.Companion  firestoreToHabitSection 9com.example.habits9.data.HabitSectionRepository.Companion  habitSectionToFirestore 9com.example.habits9.data.HabitSectionRepository.Companion  java 9com.example.habits9.data.HabitSectionRepository.Companion  let 9com.example.habits9.data.HabitSectionRepository.Companion  
mapNotNull 9com.example.habits9.data.HabitSectionRepository.Companion  BY_NAME &com.example.habits9.data.HabitSortType  
BY_SECTION &com.example.habits9.data.HabitSortType  CUSTOM_ORDER &com.example.habits9.data.HabitSortType  name &com.example.habits9.data.HabitSortType  valueOf &com.example.habits9.data.HabitSortType  	Companion "com.example.habits9.data.HabitType  	HabitType "com.example.habits9.data.HabitType  IllegalStateException "com.example.habits9.data.HabitType  Int "com.example.habits9.data.HabitType  	NUMERICAL "com.example.habits9.data.HabitType  YES_NO "com.example.habits9.data.HabitType  name "com.example.habits9.data.HabitType  value "com.example.habits9.data.HabitType  IllegalStateException ,com.example.habits9.data.HabitType.Companion  	NUMERICAL ,com.example.habits9.data.HabitType.Companion  YES_NO ,com.example.habits9.data.HabitType.Companion  AT_LEAST +com.example.habits9.data.NumericalHabitType  AT_MOST +com.example.habits9.data.NumericalHabitType  	Companion +com.example.habits9.data.NumericalHabitType  IllegalStateException +com.example.habits9.data.NumericalHabitType  Int +com.example.habits9.data.NumericalHabitType  NumericalHabitType +com.example.habits9.data.NumericalHabitType  fromInt +com.example.habits9.data.NumericalHabitType  value +com.example.habits9.data.NumericalHabitType  AT_LEAST 5com.example.habits9.data.NumericalHabitType.Companion  AT_MOST 5com.example.habits9.data.NumericalHabitType.Companion  IllegalStateException 5com.example.habits9.data.NumericalHabitType.Companion  fromInt 5com.example.habits9.data.NumericalHabitType.Companion  ApplicationContext 2com.example.habits9.data.UserPreferencesRepository  Context 2com.example.habits9.data.UserPreferencesRepository  Flow 2com.example.habits9.data.UserPreferencesRepository  
HabitSortType 2com.example.habits9.data.UserPreferencesRepository  IllegalArgumentException 2com.example.habits9.data.UserPreferencesRepository  Inject 2com.example.habits9.data.UserPreferencesRepository  List 2com.example.habits9.data.UserPreferencesRepository  PreferencesKeys 2com.example.habits9.data.UserPreferencesRepository  String 2com.example.habits9.data.UserPreferencesRepository  context 2com.example.habits9.data.UserPreferencesRepository  customHabitOrder 2com.example.habits9.data.UserPreferencesRepository  	dataStore 2com.example.habits9.data.UserPreferencesRepository  edit 2com.example.habits9.data.UserPreferencesRepository  	emptyList 2com.example.habits9.data.UserPreferencesRepository  filter 2com.example.habits9.data.UserPreferencesRepository  firstDayOfWeek 2com.example.habits9.data.UserPreferencesRepository  
habitSortType 2com.example.habits9.data.UserPreferencesRepository  isBlank 2com.example.habits9.data.UserPreferencesRepository  
isNotBlank 2com.example.habits9.data.UserPreferencesRepository  joinToString 2com.example.habits9.data.UserPreferencesRepository  map 2com.example.habits9.data.UserPreferencesRepository  split 2com.example.habits9.data.UserPreferencesRepository  stringPreferencesKey 2com.example.habits9.data.UserPreferencesRepository  updateCustomHabitOrder 2com.example.habits9.data.UserPreferencesRepository  updateFirstDayOfWeek 2com.example.habits9.data.UserPreferencesRepository  updateHabitSortType 2com.example.habits9.data.UserPreferencesRepository  CUSTOM_HABIT_ORDER Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  FIRST_DAY_OF_WEEK Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  HABIT_SORT_TYPE Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  stringPreferencesKey Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  
AnalyticsDemo "com.example.habits9.data.analytics  AnalyticsVerification "com.example.habits9.data.analytics  Any "com.example.habits9.data.analytics  Boolean "com.example.habits9.data.analytics  ChartDataPoint "com.example.habits9.data.analytics  
Completion "com.example.habits9.data.analytics  CompletionRepository "com.example.habits9.data.analytics  DateTimeFormatter "com.example.habits9.data.analytics  Double "com.example.habits9.data.analytics  	Exception "com.example.habits9.data.analytics  Float "com.example.habits9.data.analytics  GeneralInfo "com.example.habits9.data.analytics  Habit "com.example.habits9.data.analytics  HabitAnalyticsRepository "com.example.habits9.data.analytics  HabitAnalyticsUseCase "com.example.habits9.data.analytics  HabitRepository "com.example.habits9.data.analytics  HabitScheduler "com.example.habits9.data.analytics  	HabitType "com.example.habits9.data.analytics  Inject "com.example.habits9.data.analytics  Int "com.example.habits9.data.analytics  List "com.example.habits9.data.analytics  	LocalDate "com.example.habits9.data.analytics  Locale "com.example.habits9.data.analytics  Log "com.example.habits9.data.analytics  Long "com.example.habits9.data.analytics  Map "com.example.habits9.data.analytics  MeasurableHabitAnalytics "com.example.habits9.data.analytics  NumericalHabitType "com.example.habits9.data.analytics  	Singleton "com.example.habits9.data.analytics  String "com.example.habits9.data.analytics  
TimePeriod "com.example.habits9.data.analytics  
WeekFields "com.example.habits9.data.analytics  YesNoHabitAnalytics "com.example.habits9.data.analytics  ZoneId "com.example.habits9.data.analytics  assert "com.example.habits9.data.analytics  average "com.example.habits9.data.analytics  
component1 "com.example.habits9.data.analytics  
component2 "com.example.habits9.data.analytics  contains "com.example.habits9.data.analytics  count "com.example.habits9.data.analytics  distinct "com.example.habits9.data.analytics  	emptyList "com.example.habits9.data.analytics  emptyMap "com.example.habits9.data.analytics  filter "com.example.habits9.data.analytics  find "com.example.habits9.data.analytics  first "com.example.habits9.data.analytics  forEach "com.example.habits9.data.analytics  format "com.example.habits9.data.analytics  isHabitScheduled "com.example.habits9.data.analytics  let "com.example.habits9.data.analytics  listOf "com.example.habits9.data.analytics  map "com.example.habits9.data.analytics  
mapNotNull "com.example.habits9.data.analytics  	mapValues "com.example.habits9.data.analytics  maxOf "com.example.habits9.data.analytics  	maxOrNull "com.example.habits9.data.analytics  
mutableListOf "com.example.habits9.data.analytics  mutableMapOf "com.example.habits9.data.analytics  set "com.example.habits9.data.analytics  sorted "com.example.habits9.data.analytics  
startsWith "com.example.habits9.data.analytics  sumOf "com.example.habits9.data.analytics  take "com.example.habits9.data.analytics  to "com.example.habits9.data.analytics  toDoubleOrNull "com.example.habits9.data.analytics  until "com.example.habits9.data.analytics  	HabitType 0com.example.habits9.data.analytics.AnalyticsDemo  Log 0com.example.habits9.data.analytics.AnalyticsDemo  String 0com.example.habits9.data.analytics.AnalyticsDemo  TAG 0com.example.habits9.data.analytics.AnalyticsDemo  
TimePeriod 0com.example.habits9.data.analytics.AnalyticsDemo  average 0com.example.habits9.data.analytics.AnalyticsDemo  
component1 0com.example.habits9.data.analytics.AnalyticsDemo  
component2 0com.example.habits9.data.analytics.AnalyticsDemo  count 0com.example.habits9.data.analytics.AnalyticsDemo  demonstrateCalendarData 0com.example.habits9.data.analytics.AnalyticsDemo  demonstrateChartData 0com.example.habits9.data.analytics.AnalyticsDemo  demonstrateMeasurableAnalytics 0com.example.habits9.data.analytics.AnalyticsDemo  demonstrateYesNoAnalytics 0com.example.habits9.data.analytics.AnalyticsDemo  format 0com.example.habits9.data.analytics.AnalyticsDemo  listOf 0com.example.habits9.data.analytics.AnalyticsDemo  sumOf 0com.example.habits9.data.analytics.AnalyticsDemo  take 0com.example.habits9.data.analytics.AnalyticsDemo  to 0com.example.habits9.data.analytics.AnalyticsDemo  
Completion 8com.example.habits9.data.analytics.AnalyticsVerification  DateTimeFormatter 8com.example.habits9.data.analytics.AnalyticsVerification  Habit 8com.example.habits9.data.analytics.AnalyticsVerification  	LocalDate 8com.example.habits9.data.analytics.AnalyticsVerification  Locale 8com.example.habits9.data.analytics.AnalyticsVerification  Log 8com.example.habits9.data.analytics.AnalyticsVerification  NumericalHabitType 8com.example.habits9.data.analytics.AnalyticsVerification  TAG 8com.example.habits9.data.analytics.AnalyticsVerification  
WeekFields 8com.example.habits9.data.analytics.AnalyticsVerification  ZoneId 8com.example.habits9.data.analytics.AnalyticsVerification  assert 8com.example.habits9.data.analytics.AnalyticsVerification  contains 8com.example.habits9.data.analytics.AnalyticsVerification  createTestMeasurableHabit 8com.example.habits9.data.analytics.AnalyticsVerification  createTestYesNoHabit 8com.example.habits9.data.analytics.AnalyticsVerification  formatMonthLabel 8com.example.habits9.data.analytics.AnalyticsVerification  formatQuarterLabel 8com.example.habits9.data.analytics.AnalyticsVerification  formatWeekLabel 8com.example.habits9.data.analytics.AnalyticsVerification  formatYearLabel 8com.example.habits9.data.analytics.AnalyticsVerification  getDayStart 8com.example.habits9.data.analytics.AnalyticsVerification  listOf 8com.example.habits9.data.analytics.AnalyticsVerification  
mapNotNull 8com.example.habits9.data.analytics.AnalyticsVerification  	maxOrNull 8com.example.habits9.data.analytics.AnalyticsVerification  
mutableListOf 8com.example.habits9.data.analytics.AnalyticsVerification  
startsWith 8com.example.habits9.data.analytics.AnalyticsVerification  sumOf 8com.example.habits9.data.analytics.AnalyticsVerification  toDoubleOrNull 8com.example.habits9.data.analytics.AnalyticsVerification  verifyCalendarDataStructure 8com.example.habits9.data.analytics.AnalyticsVerification  verifyChartDataFormatting 8com.example.habits9.data.analytics.AnalyticsVerification  verifyGeneralInfo 8com.example.habits9.data.analytics.AnalyticsVerification  verifyMeasurableHabitAnalytics 8com.example.habits9.data.analytics.AnalyticsVerification  verifyYesNoHabitAnalytics 8com.example.habits9.data.analytics.AnalyticsVerification  label 1com.example.habits9.data.analytics.ChartDataPoint  value 1com.example.habits9.data.analytics.ChartDataPoint  currentDate .com.example.habits9.data.analytics.GeneralInfo  currentWeek .com.example.habits9.data.analytics.GeneralInfo  	HabitType ;com.example.habits9.data.analytics.HabitAnalyticsRepository  MeasurableHabitAnalytics ;com.example.habits9.data.analytics.HabitAnalyticsRepository  
TimePeriod ;com.example.habits9.data.analytics.HabitAnalyticsRepository  YesNoHabitAnalytics ;com.example.habits9.data.analytics.HabitAnalyticsRepository  analyticsUseCase ;com.example.habits9.data.analytics.HabitAnalyticsRepository  
component1 ;com.example.habits9.data.analytics.HabitAnalyticsRepository  
component2 ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getAveragePerCompletion ;com.example.habits9.data.analytics.HabitAnalyticsRepository  
getBestDay ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getCalendarData ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getCompletionHistory ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getCompletionRate ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getCurrentStreak ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getGeneralInfo ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getLongestStreak ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getMeasurableHabitAnalytics ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getTotalAmount ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getTotalCompletions ;com.example.habits9.data.analytics.HabitAnalyticsRepository  getYesNoHabitAnalytics ;com.example.habits9.data.analytics.HabitAnalyticsRepository  	mapValues ;com.example.habits9.data.analytics.HabitAnalyticsRepository  ChartDataPoint 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  DateTimeFormatter 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  GeneralInfo 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  HabitScheduler 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  	HabitType 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  	LocalDate 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  Locale 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  NumericalHabitType 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
TimePeriod 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
WeekFields 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  ZoneId 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  areConsecutiveScheduledDays 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  calculatePeriodValue 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  completionRepository 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  count 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  distinct 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  	emptyList 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  emptyMap 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  filter 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  find 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  first 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  formatPeriodLabel 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getAveragePerCompletion 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
getBestDay 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCalendarData 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCompletionHistory 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCompletionRate 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getCurrentStreak 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getGeneralInfo 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getHabitById 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getLongestStreak 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getTotalAmount 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getTotalCompletions 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  habitRepository 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  isHabitCompletedForCompletion 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  isHabitCompletedOnDay 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  isHabitScheduled 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  let 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  map 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
mapNotNull 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  maxOf 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  	maxOrNull 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
mutableListOf 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  mutableMapOf 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  set 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  sorted 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  sumOf 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  toDoubleOrNull 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  until 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  averagePerCompletion ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  bestDay ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  calendarData ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  completionHistory ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  completionRate ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  copy ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  
currentStreak ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  let ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  
longestStreak ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  totalAmount ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  totalCompletions ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  MONTH -com.example.habits9.data.analytics.TimePeriod  QUARTER -com.example.habits9.data.analytics.TimePeriod  WEEK -com.example.habits9.data.analytics.TimePeriod  YEAR -com.example.habits9.data.analytics.TimePeriod  to -com.example.habits9.data.analytics.TimePeriod  calendarData 6com.example.habits9.data.analytics.YesNoHabitAnalytics  completionHistory 6com.example.habits9.data.analytics.YesNoHabitAnalytics  completionRate 6com.example.habits9.data.analytics.YesNoHabitAnalytics  copy 6com.example.habits9.data.analytics.YesNoHabitAnalytics  
currentStreak 6com.example.habits9.data.analytics.YesNoHabitAnalytics  let 6com.example.habits9.data.analytics.YesNoHabitAnalytics  
longestStreak 6com.example.habits9.data.analytics.YesNoHabitAnalytics  totalCompletions 6com.example.habits9.data.analytics.YesNoHabitAnalytics  Boolean "com.example.habits9.data.firestore  
Completion "com.example.habits9.data.firestore  Double "com.example.habits9.data.firestore  FirestoreCompletion "com.example.habits9.data.firestore  FirestoreConverters "com.example.habits9.data.firestore  FirestoreHabit "com.example.habits9.data.firestore  FirestoreHabitSection "com.example.habits9.data.firestore  Habit "com.example.habits9.data.firestore  HabitSection "com.example.habits9.data.firestore  Int "com.example.habits9.data.firestore  List "com.example.habits9.data.firestore  Long "com.example.habits9.data.firestore  String "com.example.habits9.data.firestore  System "com.example.habits9.data.firestore  UUID "com.example.habits9.data.firestore  	emptyList "com.example.habits9.data.firestore  kotlin "com.example.habits9.data.firestore  let "com.example.habits9.data.firestore  replace "com.example.habits9.data.firestore  copy 6com.example.habits9.data.firestore.FirestoreCompletion  habitId 6com.example.habits9.data.firestore.FirestoreCompletion  id 6com.example.habits9.data.firestore.FirestoreCompletion  let 6com.example.habits9.data.firestore.FirestoreCompletion  	timestamp 6com.example.habits9.data.firestore.FirestoreCompletion  value 6com.example.habits9.data.firestore.FirestoreCompletion  
Completion 6com.example.habits9.data.firestore.FirestoreConverters  FirestoreCompletion 6com.example.habits9.data.firestore.FirestoreConverters  FirestoreHabit 6com.example.habits9.data.firestore.FirestoreConverters  FirestoreHabitSection 6com.example.habits9.data.firestore.FirestoreConverters  Habit 6com.example.habits9.data.firestore.FirestoreConverters  HabitSection 6com.example.habits9.data.firestore.FirestoreConverters  completionToFirestore 6com.example.habits9.data.firestore.FirestoreConverters  firestoreToCompletion 6com.example.habits9.data.firestore.FirestoreConverters  firestoreToHabit 6com.example.habits9.data.firestore.FirestoreConverters  firestoreToHabitSection 6com.example.habits9.data.firestore.FirestoreConverters  habitSectionToFirestore 6com.example.habits9.data.firestore.FirestoreConverters  kotlin 6com.example.habits9.data.firestore.FirestoreConverters  let 6com.example.habits9.data.firestore.FirestoreConverters  System 1com.example.habits9.data.firestore.FirestoreHabit  UUID 1com.example.habits9.data.firestore.FirestoreHabit  color 1com.example.habits9.data.firestore.FirestoreHabit  completionDatesJson 1com.example.habits9.data.firestore.FirestoreHabit  copy 1com.example.habits9.data.firestore.FirestoreHabit  	createdAt 1com.example.habits9.data.firestore.FirestoreHabit  creationDate 1com.example.habits9.data.firestore.FirestoreHabit  
currentStreak 1com.example.habits9.data.firestore.FirestoreHabit  customOrderIndex 1com.example.habits9.data.firestore.FirestoreHabit  
dayOfMonth 1com.example.habits9.data.firestore.FirestoreHabit  dayOfWeekInMonth 1com.example.habits9.data.firestore.FirestoreHabit  
daysOfWeek 1com.example.habits9.data.firestore.FirestoreHabit  description 1com.example.habits9.data.firestore.FirestoreHabit  	emptyList 1com.example.habits9.data.firestore.FirestoreHabit  
frequencyType 1com.example.habits9.data.firestore.FirestoreHabit  id 1com.example.habits9.data.firestore.FirestoreHabit  
isArchived 1com.example.habits9.data.firestore.FirestoreHabit  let 1com.example.habits9.data.firestore.FirestoreHabit  name 1com.example.habits9.data.firestore.FirestoreHabit  position 1com.example.habits9.data.firestore.FirestoreHabit  repeatsEvery 1com.example.habits9.data.firestore.FirestoreHabit  replace 1com.example.habits9.data.firestore.FirestoreHabit  	sectionId 1com.example.habits9.data.firestore.FirestoreHabit  
targetType 1com.example.habits9.data.firestore.FirestoreHabit  targetValue 1com.example.habits9.data.firestore.FirestoreHabit  type 1com.example.habits9.data.firestore.FirestoreHabit  unit 1com.example.habits9.data.firestore.FirestoreHabit  uuid 1com.example.habits9.data.firestore.FirestoreHabit  weekOfMonth 1com.example.habits9.data.firestore.FirestoreHabit  color 8com.example.habits9.data.firestore.FirestoreHabitSection  copy 8com.example.habits9.data.firestore.FirestoreHabitSection  displayOrder 8com.example.habits9.data.firestore.FirestoreHabitSection  id 8com.example.habits9.data.firestore.FirestoreHabitSection  let 8com.example.habits9.data.firestore.FirestoreHabitSection  name 8com.example.habits9.data.firestore.FirestoreHabitSection  CompletionRepository com.example.habits9.di  DatabaseModule com.example.habits9.di  FirebaseAuth com.example.habits9.di  FirebaseFirestore com.example.habits9.di  HabitRepository com.example.habits9.di  HabitSectionRepository com.example.habits9.di  	InstallIn com.example.habits9.di  Module com.example.habits9.di  Provides com.example.habits9.di  	Singleton com.example.habits9.di  SingletonComponent com.example.habits9.di  CompletionRepository %com.example.habits9.di.DatabaseModule  FirebaseAuth %com.example.habits9.di.DatabaseModule  FirebaseFirestore %com.example.habits9.di.DatabaseModule  HabitRepository %com.example.habits9.di.DatabaseModule  HabitSectionRepository %com.example.habits9.di.DatabaseModule  Boolean com.example.habits9.ui  
Completion com.example.habits9.ui  CompletionRepository com.example.habits9.ui  DateTimeFormatter com.example.habits9.ui  	DayOfWeek com.example.habits9.ui  Double com.example.habits9.ui  	Exception com.example.habits9.ui  Float com.example.habits9.ui  Habit com.example.habits9.ui  HabitRepository com.example.habits9.ui  HabitScheduler com.example.habits9.ui  HabitSectionRepository com.example.habits9.ui  
HabitSortType com.example.habits9.ui  	HabitType com.example.habits9.ui  HabitWithCompletions com.example.habits9.ui  
HiltViewModel com.example.habits9.ui  Inject com.example.habits9.ui  Instant com.example.habits9.ui  Int com.example.habits9.ui  
JavaDayOfWeek com.example.habits9.ui  List com.example.habits9.ui  	LocalDate com.example.habits9.ui  Long com.example.habits9.ui  MainUiState com.example.habits9.ui  
MainViewModel com.example.habits9.ui  Map com.example.habits9.ui  MutableStateFlow com.example.habits9.ui  NumericalHabitType com.example.habits9.ui  	StateFlow com.example.habits9.ui  String com.example.habits9.ui  TemporalAdjusters com.example.habits9.ui  	ViewModel com.example.habits9.ui  WeekBoundaryUtils com.example.habits9.ui  
WeekFields com.example.habits9.ui  WeekInfo com.example.habits9.ui  WhileSubscribed com.example.habits9.ui  ZoneId com.example.habits9.ui  _completionValuesState com.example.habits9.ui  _completionsState com.example.habits9.ui  android com.example.habits9.ui  any com.example.habits9.ui  	associate com.example.habits9.ui  associateBy com.example.habits9.ui  
associateWith com.example.habits9.ui  average com.example.habits9.ui  com com.example.habits9.ui  combine com.example.habits9.ui  	compareBy com.example.habits9.ui  completionRepository com.example.habits9.ui  completionsFlow com.example.habits9.ui  
component1 com.example.habits9.ui  
component2 com.example.habits9.ui  count com.example.habits9.ui  	emptyList com.example.habits9.ui  emptyMap com.example.habits9.ui  filter com.example.habits9.ui  
flatMapLatest com.example.habits9.ui  flowOf com.example.habits9.ui  forEach com.example.habits9.ui  getCurrentWeekDates com.example.habits9.ui  
getWeekEnd com.example.habits9.ui  
getWeekNumber com.example.habits9.ui  getWeekStart com.example.habits9.ui  groupBy com.example.habits9.ui  hideMeasurableHabitDialog com.example.habits9.ui  isEmpty com.example.habits9.ui  isHabitScheduled com.example.habits9.ui  
isNotEmpty com.example.habits9.ui  
isNullOrEmpty com.example.habits9.ui  kotlinx com.example.habits9.ui  launch com.example.habits9.ui  	lowercase com.example.habits9.ui  map com.example.habits9.ui  mapKeys com.example.habits9.ui  	mapValues com.example.habits9.ui  plus com.example.habits9.ui  runBlocking com.example.habits9.ui  showMeasurableHabitDialogNew com.example.habits9.ui  
sortHabits com.example.habits9.ui  sortedBy com.example.habits9.ui  
sortedWith com.example.habits9.ui  stateIn com.example.habits9.ui  thenBy com.example.habits9.ui  to com.example.habits9.ui  toDoubleOrNull com.example.habits9.ui  toMap com.example.habits9.ui  userPreferencesRepository com.example.habits9.ui  	withIndex com.example.habits9.ui  completionValues +com.example.habits9.ui.HabitWithCompletions  completions +com.example.habits9.ui.HabitWithCompletions  copy +com.example.habits9.ui.HabitWithCompletions  
currentStreak +com.example.habits9.ui.HabitWithCompletions  habit +com.example.habits9.ui.HabitWithCompletions  
scheduledDays +com.example.habits9.ui.HabitWithCompletions  copy "com.example.habits9.ui.MainUiState  currentSortType "com.example.habits9.ui.MainUiState  habitsWithCompletions "com.example.habits9.ui.MainUiState  	isLoading "com.example.habits9.ui.MainUiState  measurableDialogCurrentValue "com.example.habits9.ui.MainUiState  measurableDialogDate "com.example.habits9.ui.MainUiState  measurableDialogHabitId "com.example.habits9.ui.MainUiState  measurableDialogHabitName "com.example.habits9.ui.MainUiState  measurableDialogTargetType "com.example.habits9.ui.MainUiState  measurableDialogTargetValue "com.example.habits9.ui.MainUiState  measurableDialogUnit "com.example.habits9.ui.MainUiState  showMeasurableDialog "com.example.habits9.ui.MainUiState  weekInfo "com.example.habits9.ui.MainUiState  Boolean $com.example.habits9.ui.MainViewModel  
Completion $com.example.habits9.ui.MainViewModel  CompletionRepository $com.example.habits9.ui.MainViewModel  DateTimeFormatter $com.example.habits9.ui.MainViewModel  	DayOfWeek $com.example.habits9.ui.MainViewModel  Double $com.example.habits9.ui.MainViewModel  	Exception $com.example.habits9.ui.MainViewModel  Float $com.example.habits9.ui.MainViewModel  Habit $com.example.habits9.ui.MainViewModel  HabitRepository $com.example.habits9.ui.MainViewModel  HabitScheduler $com.example.habits9.ui.MainViewModel  HabitSectionRepository $com.example.habits9.ui.MainViewModel  
HabitSortType $com.example.habits9.ui.MainViewModel  	HabitType $com.example.habits9.ui.MainViewModel  HabitWithCompletions $com.example.habits9.ui.MainViewModel  Inject $com.example.habits9.ui.MainViewModel  Instant $com.example.habits9.ui.MainViewModel  Int $com.example.habits9.ui.MainViewModel  
JavaDayOfWeek $com.example.habits9.ui.MainViewModel  List $com.example.habits9.ui.MainViewModel  	LocalDate $com.example.habits9.ui.MainViewModel  Long $com.example.habits9.ui.MainViewModel  MainUiState $com.example.habits9.ui.MainViewModel  Map $com.example.habits9.ui.MainViewModel  MutableStateFlow $com.example.habits9.ui.MainViewModel  NumericalHabitType $com.example.habits9.ui.MainViewModel  	StateFlow $com.example.habits9.ui.MainViewModel  String $com.example.habits9.ui.MainViewModel  TemporalAdjusters $com.example.habits9.ui.MainViewModel  WeekBoundaryUtils $com.example.habits9.ui.MainViewModel  
WeekFields $com.example.habits9.ui.MainViewModel  WeekInfo $com.example.habits9.ui.MainViewModel  WhileSubscribed $com.example.habits9.ui.MainViewModel  ZoneId $com.example.habits9.ui.MainViewModel  _completionValuesState $com.example.habits9.ui.MainViewModel  _completionsState $com.example.habits9.ui.MainViewModel  _dialogState $com.example.habits9.ui.MainViewModel  android $com.example.habits9.ui.MainViewModel  any $com.example.habits9.ui.MainViewModel  	associate $com.example.habits9.ui.MainViewModel  associateBy $com.example.habits9.ui.MainViewModel  
associateWith $com.example.habits9.ui.MainViewModel  average $com.example.habits9.ui.MainViewModel  baseEnhancedUiState $com.example.habits9.ui.MainViewModel  calculateCurrentStreak $com.example.habits9.ui.MainViewModel  #calculateDailyCompletionPercentages $com.example.habits9.ui.MainViewModel  calculateScheduledDays $com.example.habits9.ui.MainViewModel  calculateWeekInfo $com.example.habits9.ui.MainViewModel  com $com.example.habits9.ui.MainViewModel  combine $com.example.habits9.ui.MainViewModel  	compareBy $com.example.habits9.ui.MainViewModel  completionRepository $com.example.habits9.ui.MainViewModel  completionsFlow $com.example.habits9.ui.MainViewModel  
component1 $com.example.habits9.ui.MainViewModel  
component2 $com.example.habits9.ui.MainViewModel  count $com.example.habits9.ui.MainViewModel  	emptyList $com.example.habits9.ui.MainViewModel  emptyMap $com.example.habits9.ui.MainViewModel  enhancedUiState $com.example.habits9.ui.MainViewModel  filter $com.example.habits9.ui.MainViewModel  filterScheduledHabits $com.example.habits9.ui.MainViewModel  
flatMapLatest $com.example.habits9.ui.MainViewModel  flowOf $com.example.habits9.ui.MainViewModel  getCurrentWeekDates $com.example.habits9.ui.MainViewModel  getCurrentWeekStart $com.example.habits9.ui.MainViewModel  
getWeekEnd $com.example.habits9.ui.MainViewModel  
getWeekNumber $com.example.habits9.ui.MainViewModel  getWeekStart $com.example.habits9.ui.MainViewModel  groupBy $com.example.habits9.ui.MainViewModel  habitRepository $com.example.habits9.ui.MainViewModel  habitSectionRepository $com.example.habits9.ui.MainViewModel  hideMeasurableHabitDialog $com.example.habits9.ui.MainViewModel  isEmpty $com.example.habits9.ui.MainViewModel  isHabitScheduled $com.example.habits9.ui.MainViewModel  
isNotEmpty $com.example.habits9.ui.MainViewModel  
isNullOrEmpty $com.example.habits9.ui.MainViewModel  kotlinx $com.example.habits9.ui.MainViewModel  launch $com.example.habits9.ui.MainViewModel  	lowercase $com.example.habits9.ui.MainViewModel  map $com.example.habits9.ui.MainViewModel  mapKeys $com.example.habits9.ui.MainViewModel  	mapValues $com.example.habits9.ui.MainViewModel  onCellClick $com.example.habits9.ui.MainViewModel  plus $com.example.habits9.ui.MainViewModel  runBlocking $com.example.habits9.ui.MainViewModel  saveMeasurableHabitCompletion $com.example.habits9.ui.MainViewModel  showMeasurableHabitDialogNew $com.example.habits9.ui.MainViewModel  
sortHabits $com.example.habits9.ui.MainViewModel  sortedBy $com.example.habits9.ui.MainViewModel  
sortedWith $com.example.habits9.ui.MainViewModel  stateIn $com.example.habits9.ui.MainViewModel  thenBy $com.example.habits9.ui.MainViewModel  to $com.example.habits9.ui.MainViewModel  toDoubleOrNull $com.example.habits9.ui.MainViewModel  toMap $com.example.habits9.ui.MainViewModel  uiState $com.example.habits9.ui.MainViewModel  updateMeasurableDialogValue $com.example.habits9.ui.MainViewModel  updateSortType $com.example.habits9.ui.MainViewModel  userPreferencesRepository $com.example.habits9.ui.MainViewModel  viewModelScope $com.example.habits9.ui.MainViewModel  	withIndex $com.example.habits9.ui.MainViewModel  
JavaDayOfWeek 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  TemporalAdjusters 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
WeekFields 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getCurrentWeekDates 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekEnd 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekNumber 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getWeekStart 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  map 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  example (com.example.habits9.ui.MainViewModel.com  habits9 0com.example.habits9.ui.MainViewModel.com.example  data 8com.example.habits9.ui.MainViewModel.com.example.habits9  Habit =com.example.habits9.ui.MainViewModel.com.example.habits9.data  UserPreferencesRepository =com.example.habits9.ui.MainViewModel.com.example.habits9.data  dailyCompletionPercentages com.example.habits9.ui.WeekInfo  dates com.example.habits9.ui.WeekInfo  firstDayOfWeek com.example.habits9.ui.WeekInfo  formattedDates com.example.habits9.ui.WeekInfo  formattedDayAbbreviations com.example.habits9.ui.WeekInfo  
timestamps com.example.habits9.ui.WeekInfo  
weekNumber com.example.habits9.ui.WeekInfo  weeklyCompletionPercentage com.example.habits9.ui.WeekInfo  	Alignment com.example.habits9.ui.auth  Arrangement com.example.habits9.ui.auth  
AuthScreen com.example.habits9.ui.auth  AuthUiState com.example.habits9.ui.auth  
AuthViewModel com.example.habits9.ui.auth  Boolean com.example.habits9.ui.auth  Brush com.example.habits9.ui.auth  Button com.example.habits9.ui.auth  ButtonDefaults com.example.habits9.ui.auth  CircularProgressIndicator com.example.habits9.ui.auth  Color com.example.habits9.ui.auth  Column com.example.habits9.ui.auth  
Composable com.example.habits9.ui.auth  DarkAccentPrimary com.example.habits9.ui.auth  DarkAccentPrimaryLogo com.example.habits9.ui.auth  DarkBackground com.example.habits9.ui.auth  DarkBackgroundDarker com.example.habits9.ui.auth  
DarkIconColor com.example.habits9.ui.auth  DarkSurfaceVariant com.example.habits9.ui.auth  DarkTextPrimary com.example.habits9.ui.auth  DarkTextSecondary com.example.habits9.ui.auth  	Exception com.example.habits9.ui.auth  FirebaseAuth com.example.habits9.ui.auth  FirebaseAuthException com.example.habits9.ui.auth  
FontWeight com.example.habits9.ui.auth  ForgotPasswordDialog com.example.habits9.ui.auth  
HiltViewModel com.example.habits9.ui.auth  Icon com.example.habits9.ui.auth  Icons com.example.habits9.ui.auth  	Indicator com.example.habits9.ui.auth  Inject com.example.habits9.ui.auth  KeyboardOptions com.example.habits9.ui.auth  KeyboardType com.example.habits9.ui.auth  LightAccentPrimary com.example.habits9.ui.auth  LightAccentPrimaryLogo com.example.habits9.ui.auth  LightBackground com.example.habits9.ui.auth  LightBackgroundDarker com.example.habits9.ui.auth  
LightError com.example.habits9.ui.auth  LightIconColor com.example.habits9.ui.auth  LightSurfaceVariant com.example.habits9.ui.auth  LightTextPrimary com.example.habits9.ui.auth  LightTextSecondary com.example.habits9.ui.auth  
MaterialTheme com.example.habits9.ui.auth  Modifier com.example.habits9.ui.auth  MutableStateFlow com.example.habits9.ui.auth  OutlinedButton com.example.habits9.ui.auth  OutlinedTextField com.example.habits9.ui.auth  OutlinedTextFieldDefaults com.example.habits9.ui.auth  PasswordVisualTransformation com.example.habits9.ui.auth  RoundedCornerShape com.example.habits9.ui.auth  Row com.example.habits9.ui.auth  Spacer com.example.habits9.ui.auth  	StateFlow com.example.habits9.ui.auth  String com.example.habits9.ui.auth  Tab com.example.habits9.ui.auth  TabRow com.example.habits9.ui.auth  TabRowDefaults com.example.habits9.ui.auth  Text com.example.habits9.ui.auth  	TextAlign com.example.habits9.ui.auth  
TextButton com.example.habits9.ui.auth  Unit com.example.habits9.ui.auth  VerificationScreen com.example.habits9.ui.auth  	ViewModel com.example.habits9.ui.auth  _uiState com.example.habits9.ui.auth  asStateFlow com.example.habits9.ui.auth  await com.example.habits9.ui.auth  buttonColors com.example.habits9.ui.auth  	clickable com.example.habits9.ui.auth  clip com.example.habits9.ui.auth  colors com.example.habits9.ui.auth  fillMaxSize com.example.habits9.ui.auth  fillMaxWidth com.example.habits9.ui.auth  firebaseAuth com.example.habits9.ui.auth  height com.example.habits9.ui.auth  isBlank com.example.habits9.ui.auth  
isNotBlank com.example.habits9.ui.auth  
isNotEmpty com.example.habits9.ui.auth  launch com.example.habits9.ui.auth  listOf com.example.habits9.ui.auth  outlinedButtonColors com.example.habits9.ui.auth  padding com.example.habits9.ui.auth  provideDelegate com.example.habits9.ui.auth  size com.example.habits9.ui.auth  spacedBy com.example.habits9.ui.auth  tabIndicatorOffset com.example.habits9.ui.auth  updateEmail com.example.habits9.ui.auth  updatePassword com.example.habits9.ui.auth  verticalGradient com.example.habits9.ui.auth  copy 'com.example.habits9.ui.auth.AuthUiState  email 'com.example.habits9.ui.auth.AuthUiState  errorMessage 'com.example.habits9.ui.auth.AuthUiState  	isLoading 'com.example.habits9.ui.auth.AuthUiState  password 'com.example.habits9.ui.auth.AuthUiState  AuthUiState )com.example.habits9.ui.auth.AuthViewModel  FirebaseAuth )com.example.habits9.ui.auth.AuthViewModel  MutableStateFlow )com.example.habits9.ui.auth.AuthViewModel  _uiState )com.example.habits9.ui.auth.AuthViewModel  asStateFlow )com.example.habits9.ui.auth.AuthViewModel  await )com.example.habits9.ui.auth.AuthViewModel  checkAuthState )com.example.habits9.ui.auth.AuthViewModel  firebaseAuth )com.example.habits9.ui.auth.AuthViewModel  isBlank )com.example.habits9.ui.auth.AuthViewModel  launch )com.example.habits9.ui.auth.AuthViewModel  sendPasswordResetEmail )com.example.habits9.ui.auth.AuthViewModel  signIn )com.example.habits9.ui.auth.AuthViewModel  signUp )com.example.habits9.ui.auth.AuthViewModel  uiState )com.example.habits9.ui.auth.AuthViewModel  updateEmail )com.example.habits9.ui.auth.AuthViewModel  updatePassword )com.example.habits9.ui.auth.AuthViewModel  viewModelScope )com.example.habits9.ui.auth.AuthViewModel  example com.example.habits9.ui.com  habits9 "com.example.habits9.ui.com.example  data *com.example.habits9.ui.com.example.habits9  Habit /com.example.habits9.ui.com.example.habits9.data  UserPreferencesRepository /com.example.habits9.ui.com.example.habits9.data  
AccentPrimary !com.example.habits9.ui.components  AlertDialog !com.example.habits9.ui.components  	Alignment !com.example.habits9.ui.components  Arrangement !com.example.habits9.ui.components  BackgroundDark !com.example.habits9.ui.components  Boolean !com.example.habits9.ui.components  BorderStroke !com.example.habits9.ui.components  Box !com.example.habits9.ui.components  Button !com.example.habits9.ui.components  ButtonDefaults !com.example.habits9.ui.components  Card !com.example.habits9.ui.components  CardDefaults !com.example.habits9.ui.components  Column !com.example.habits9.ui.components  
Composable !com.example.habits9.ui.components  DarkBackground !com.example.habits9.ui.components  DayChip !com.example.habits9.ui.components  	DayOfWeek !com.example.habits9.ui.components  Divider !com.example.habits9.ui.components  DividerColor !com.example.habits9.ui.components  Double !com.example.habits9.ui.components  DropdownMenu !com.example.habits9.ui.components  EnhancedFrequency !com.example.habits9.ui.components  EnhancedFrequencyPickerDialog !com.example.habits9.ui.components  ExperimentalMaterial3Api !com.example.habits9.ui.components  
FontWeight !com.example.habits9.ui.components  
FrequencyType !com.example.habits9.ui.components  FrequencyTypeTab !com.example.habits9.ui.components  
HabitSortType !com.example.habits9.ui.components  Icon !com.example.habits9.ui.components  
IconButton !com.example.habits9.ui.components  Icons !com.example.habits9.ui.components  	ImeAction !com.example.habits9.ui.components  Int !com.example.habits9.ui.components  KeyboardActions !com.example.habits9.ui.components  KeyboardOptions !com.example.habits9.ui.components  KeyboardType !com.example.habits9.ui.components  LaunchedEffect !com.example.habits9.ui.components  LazyRow !com.example.habits9.ui.components  
MaterialTheme !com.example.habits9.ui.components  Modifier !com.example.habits9.ui.components  MonthlyOptions !com.example.habits9.ui.components  NumericalInputDialog !com.example.habits9.ui.components  OptIn !com.example.habits9.ui.components  OutlinedButton !com.example.habits9.ui.components  OutlinedTextField !com.example.habits9.ui.components  OutlinedTextFieldDefaults !com.example.habits9.ui.components  RoundedCornerShape !com.example.habits9.ui.components  Row !com.example.habits9.ui.components  Set !com.example.habits9.ui.components  SortMenuButton !com.example.habits9.ui.components  SortMenuItem !com.example.habits9.ui.components  Spacer !com.example.habits9.ui.components  String !com.example.habits9.ui.components  SurfaceVariantDark !com.example.habits9.ui.components  Text !com.example.habits9.ui.components  	TextAlign !com.example.habits9.ui.components  
TextButton !com.example.habits9.ui.components  TextPrimary !com.example.habits9.ui.components  
TextSecondary !com.example.habits9.ui.components  Unit !com.example.habits9.ui.components  
WeeklyOptions !com.example.habits9.ui.components  android !com.example.habits9.ui.components  
background !com.example.habits9.ui.components  buttonColors !com.example.habits9.ui.components  
cardColors !com.example.habits9.ui.components  colors !com.example.habits9.ui.components  fillMaxWidth !com.example.habits9.ui.components  focusRequester !com.example.habits9.ui.components  forEach !com.example.habits9.ui.components  getValue !com.example.habits9.ui.components  height !com.example.habits9.ui.components  
isNotEmpty !com.example.habits9.ui.components  let !com.example.habits9.ui.components  	lowercase !com.example.habits9.ui.components  minus !com.example.habits9.ui.components  mutableStateOf !com.example.habits9.ui.components  outlinedButtonColors !com.example.habits9.ui.components  padding !com.example.habits9.ui.components  plus !com.example.habits9.ui.components  provideDelegate !com.example.habits9.ui.components  remember !com.example.habits9.ui.components  replaceFirstChar !com.example.habits9.ui.components  setOf !com.example.habits9.ui.components  setValue !com.example.habits9.ui.components  size !com.example.habits9.ui.components  spacedBy !com.example.habits9.ui.components  take !com.example.habits9.ui.components  toDoubleOrNull !com.example.habits9.ui.components  toIntOrNull !com.example.habits9.ui.components  toList !com.example.habits9.ui.components  toSet !com.example.habits9.ui.components  	uppercase !com.example.habits9.ui.components  weight !com.example.habits9.ui.components  width !com.example.habits9.ui.components  Boolean "com.example.habits9.ui.createhabit  CreateHabitUiState "com.example.habits9.ui.createhabit  CreateHabitViewModel "com.example.habits9.ui.createhabit  	DayOfWeek "com.example.habits9.ui.createhabit  EnhancedFrequency "com.example.habits9.ui.createhabit  	Exception "com.example.habits9.ui.createhabit  HabitFrequency "com.example.habits9.ui.createhabit  HabitSection "com.example.habits9.ui.createhabit  HabitSectionRepository "com.example.habits9.ui.createhabit  
HiltViewModel "com.example.habits9.ui.createhabit  Inject "com.example.habits9.ui.createhabit  Int "com.example.habits9.ui.createhabit  List "com.example.habits9.ui.createhabit  	LocalTime "com.example.habits9.ui.createhabit  MutableStateFlow "com.example.habits9.ui.createhabit  
ReminderState "com.example.habits9.ui.createhabit  Set "com.example.habits9.ui.createhabit  	StateFlow "com.example.habits9.ui.createhabit  String "com.example.habits9.ui.createhabit  Unit "com.example.habits9.ui.createhabit  	ViewModel "com.example.habits9.ui.createhabit  _uiState "com.example.habits9.ui.createhabit  asStateFlow "com.example.habits9.ui.createhabit  com "com.example.habits9.ui.createhabit  	emptyList "com.example.habits9.ui.createhabit  first "com.example.habits9.ui.createhabit  format "com.example.habits9.ui.createhabit  habitRepository "com.example.habits9.ui.createhabit  habitSectionRepository "com.example.habits9.ui.createhabit  isBlank "com.example.habits9.ui.createhabit  
isNotEmpty "com.example.habits9.ui.createhabit  joinToString "com.example.habits9.ui.createhabit  launch "com.example.habits9.ui.createhabit  	lowercase "com.example.habits9.ui.createhabit  map "com.example.habits9.ui.createhabit  replaceFirstChar "com.example.habits9.ui.createhabit  setOf "com.example.habits9.ui.createhabit  sortedBy "com.example.habits9.ui.createhabit  toDoubleOrNull "com.example.habits9.ui.createhabit  	uppercase "com.example.habits9.ui.createhabit  availableSections 5com.example.habits9.ui.createhabit.CreateHabitUiState  copy 5com.example.habits9.ui.createhabit.CreateHabitUiState  enhancedFrequency 5com.example.habits9.ui.createhabit.CreateHabitUiState  
reminderState 5com.example.habits9.ui.createhabit.CreateHabitUiState  
selectedColor 5com.example.habits9.ui.createhabit.CreateHabitUiState  selectedSection 5com.example.habits9.ui.createhabit.CreateHabitUiState  showReminderDialog 5com.example.habits9.ui.createhabit.CreateHabitUiState  showSectionSelector 5com.example.habits9.ui.createhabit.CreateHabitUiState  CreateHabitUiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  MutableStateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  _uiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  asStateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  com 7com.example.habits9.ui.createhabit.CreateHabitViewModel  first 7com.example.habits9.ui.createhabit.CreateHabitViewModel  habitRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  habitSectionRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  hideReminderDialog 7com.example.habits9.ui.createhabit.CreateHabitViewModel  hideSectionSelector 7com.example.habits9.ui.createhabit.CreateHabitViewModel  isBlank 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
isNotEmpty 7com.example.habits9.ui.createhabit.CreateHabitViewModel  launch 7com.example.habits9.ui.createhabit.CreateHabitViewModel  loadSections 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	saveHabit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
selectSection 7com.example.habits9.ui.createhabit.CreateHabitViewModel  showReminderDialog 7com.example.habits9.ui.createhabit.CreateHabitViewModel  showSectionSelector 7com.example.habits9.ui.createhabit.CreateHabitViewModel  toDoubleOrNull 7com.example.habits9.ui.createhabit.CreateHabitViewModel  uiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  updateEnhancedFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  updateReminder 7com.example.habits9.ui.createhabit.CreateHabitViewModel  viewModelScope 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	Companion 1com.example.habits9.ui.createhabit.HabitFrequency  DAILY 1com.example.habits9.ui.createhabit.HabitFrequency  EnhancedFrequency 1com.example.habits9.ui.createhabit.HabitFrequency  
FrequencyType 1com.example.habits9.ui.createhabit.HabitFrequency  HabitFrequency 1com.example.habits9.ui.createhabit.HabitFrequency  Int 1com.example.habits9.ui.createhabit.HabitFrequency  String 1com.example.habits9.ui.createhabit.HabitFrequency  denominator 1com.example.habits9.ui.createhabit.HabitFrequency  	emptyList 1com.example.habits9.ui.createhabit.HabitFrequency  	numerator 1com.example.habits9.ui.createhabit.HabitFrequency  DAILY ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  HabitFrequency ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  	DayOfWeek 0com.example.habits9.ui.createhabit.ReminderState  String 0com.example.habits9.ui.createhabit.ReminderState  first 0com.example.habits9.ui.createhabit.ReminderState  format 0com.example.habits9.ui.createhabit.ReminderState  	isEnabled 0com.example.habits9.ui.createhabit.ReminderState  joinToString 0com.example.habits9.ui.createhabit.ReminderState  	lowercase 0com.example.habits9.ui.createhabit.ReminderState  map 0com.example.habits9.ui.createhabit.ReminderState  replaceFirstChar 0com.example.habits9.ui.createhabit.ReminderState  selectedDays 0com.example.habits9.ui.createhabit.ReminderState  sortedBy 0com.example.habits9.ui.createhabit.ReminderState  time 0com.example.habits9.ui.createhabit.ReminderState  toDisplayString 0com.example.habits9.ui.createhabit.ReminderState  	uppercase 0com.example.habits9.ui.createhabit.ReminderState  example &com.example.habits9.ui.createhabit.com  habits9 .com.example.habits9.ui.createhabit.com.example  data 6com.example.habits9.ui.createhabit.com.example.habits9  HabitRepository ;com.example.habits9.ui.createhabit.com.example.habits9.data  	HabitType ;com.example.habits9.ui.createhabit.com.example.habits9.data  
AccentPrimary ,com.example.habits9.ui.createmeasurablehabit  AlertDialog ,com.example.habits9.ui.createmeasurablehabit  	Alignment ,com.example.habits9.ui.createmeasurablehabit  Arrangement ,com.example.habits9.ui.createmeasurablehabit  Box ,com.example.habits9.ui.createmeasurablehabit  CircleShape ,com.example.habits9.ui.createmeasurablehabit  Color ,com.example.habits9.ui.createmeasurablehabit  Column ,com.example.habits9.ui.createmeasurablehabit  
Composable ,com.example.habits9.ui.createmeasurablehabit  CreateHabitViewModel ,com.example.habits9.ui.createmeasurablehabit  CreateMeasurableHabitScreen ,com.example.habits9.ui.createmeasurablehabit  DarkBackground ,com.example.habits9.ui.createmeasurablehabit  	DayOfWeek ,com.example.habits9.ui.createmeasurablehabit  DividerColor ,com.example.habits9.ui.createmeasurablehabit  DropdownMenuItem ,com.example.habits9.ui.createmeasurablehabit  ExperimentalMaterial3Api ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuBox ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuDefaults ,com.example.habits9.ui.createmeasurablehabit  
FontWeight ,com.example.habits9.ui.createmeasurablehabit  Icon ,com.example.habits9.ui.createmeasurablehabit  
IconButton ,com.example.habits9.ui.createmeasurablehabit  Icons ,com.example.habits9.ui.createmeasurablehabit  LaunchedEffect ,com.example.habits9.ui.createmeasurablehabit  List ,com.example.habits9.ui.createmeasurablehabit  	LocalTime ,com.example.habits9.ui.createmeasurablehabit  Modifier ,com.example.habits9.ui.createmeasurablehabit  OptIn ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextField ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextFieldDefaults ,com.example.habits9.ui.createmeasurablehabit  ReminderDialog ,com.example.habits9.ui.createmeasurablehabit  
ReminderState ,com.example.habits9.ui.createmeasurablehabit  Row ,com.example.habits9.ui.createmeasurablehabit  Scaffold ,com.example.habits9.ui.createmeasurablehabit  SectionSelectorDialog ,com.example.habits9.ui.createmeasurablehabit  Spacer ,com.example.habits9.ui.createmeasurablehabit  String ,com.example.habits9.ui.createmeasurablehabit  SurfaceVariantDark ,com.example.habits9.ui.createmeasurablehabit  Switch ,com.example.habits9.ui.createmeasurablehabit  SwitchDefaults ,com.example.habits9.ui.createmeasurablehabit  Text ,com.example.habits9.ui.createmeasurablehabit  
TextButton ,com.example.habits9.ui.createmeasurablehabit  TextPrimary ,com.example.habits9.ui.createmeasurablehabit  
TextSecondary ,com.example.habits9.ui.createmeasurablehabit  
TimePicker ,com.example.habits9.ui.createmeasurablehabit  TimePickerDefaults ,com.example.habits9.ui.createmeasurablehabit  TimePickerDialog ,com.example.habits9.ui.createmeasurablehabit  	TopAppBar ,com.example.habits9.ui.createmeasurablehabit  TopAppBarDefaults ,com.example.habits9.ui.createmeasurablehabit  TrailingIcon ,com.example.habits9.ui.createmeasurablehabit  Unit ,com.example.habits9.ui.createmeasurablehabit  
background ,com.example.habits9.ui.createmeasurablehabit  	clickable ,com.example.habits9.ui.createmeasurablehabit  collectAsState ,com.example.habits9.ui.createmeasurablehabit  colors ,com.example.habits9.ui.createmeasurablehabit  com ,com.example.habits9.ui.createmeasurablehabit  fillMaxSize ,com.example.habits9.ui.createmeasurablehabit  fillMaxWidth ,com.example.habits9.ui.createmeasurablehabit  forEach ,com.example.habits9.ui.createmeasurablehabit  format ,com.example.habits9.ui.createmeasurablehabit  getValue ,com.example.habits9.ui.createmeasurablehabit  height ,com.example.habits9.ui.createmeasurablehabit  
isNotEmpty ,com.example.habits9.ui.createmeasurablehabit  kotlinx ,com.example.habits9.ui.createmeasurablehabit  listOf ,com.example.habits9.ui.createmeasurablehabit  minus ,com.example.habits9.ui.createmeasurablehabit  mutableStateOf ,com.example.habits9.ui.createmeasurablehabit  padding ,com.example.habits9.ui.createmeasurablehabit  plus ,com.example.habits9.ui.createmeasurablehabit  provideDelegate ,com.example.habits9.ui.createmeasurablehabit  remember ,com.example.habits9.ui.createmeasurablehabit  rememberTimePickerState ,com.example.habits9.ui.createmeasurablehabit  setValue ,com.example.habits9.ui.createmeasurablehabit  size ,com.example.habits9.ui.createmeasurablehabit  to ,com.example.habits9.ui.createmeasurablehabit  topAppBarColors ,com.example.habits9.ui.createmeasurablehabit  weight ,com.example.habits9.ui.createmeasurablehabit  width ,com.example.habits9.ui.createmeasurablehabit  example 0com.example.habits9.ui.createmeasurablehabit.com  habits9 8com.example.habits9.ui.createmeasurablehabit.com.example  data @com.example.habits9.ui.createmeasurablehabit.com.example.habits9  HabitSection Ecom.example.habits9.ui.createmeasurablehabit.com.example.habits9.data  
AccentPrimary 'com.example.habits9.ui.createyesnohabit  AlertDialog 'com.example.habits9.ui.createyesnohabit  	Alignment 'com.example.habits9.ui.createyesnohabit  Arrangement 'com.example.habits9.ui.createyesnohabit  Box 'com.example.habits9.ui.createyesnohabit  CircleShape 'com.example.habits9.ui.createyesnohabit  Color 'com.example.habits9.ui.createyesnohabit  Column 'com.example.habits9.ui.createyesnohabit  
Composable 'com.example.habits9.ui.createyesnohabit  CreateHabitViewModel 'com.example.habits9.ui.createyesnohabit  CreateYesNoHabitScreen 'com.example.habits9.ui.createyesnohabit  DarkBackground 'com.example.habits9.ui.createyesnohabit  	DayOfWeek 'com.example.habits9.ui.createyesnohabit  DividerColor 'com.example.habits9.ui.createyesnohabit  ExperimentalMaterial3Api 'com.example.habits9.ui.createyesnohabit  
FontWeight 'com.example.habits9.ui.createyesnohabit  Icon 'com.example.habits9.ui.createyesnohabit  
IconButton 'com.example.habits9.ui.createyesnohabit  Icons 'com.example.habits9.ui.createyesnohabit  LaunchedEffect 'com.example.habits9.ui.createyesnohabit  List 'com.example.habits9.ui.createyesnohabit  	LocalTime 'com.example.habits9.ui.createyesnohabit  Modifier 'com.example.habits9.ui.createyesnohabit  OptIn 'com.example.habits9.ui.createyesnohabit  OutlinedTextField 'com.example.habits9.ui.createyesnohabit  OutlinedTextFieldDefaults 'com.example.habits9.ui.createyesnohabit  ReminderDialog 'com.example.habits9.ui.createyesnohabit  
ReminderState 'com.example.habits9.ui.createyesnohabit  Row 'com.example.habits9.ui.createyesnohabit  Scaffold 'com.example.habits9.ui.createyesnohabit  SectionSelectorDialog 'com.example.habits9.ui.createyesnohabit  Spacer 'com.example.habits9.ui.createyesnohabit  String 'com.example.habits9.ui.createyesnohabit  SurfaceVariantDark 'com.example.habits9.ui.createyesnohabit  Switch 'com.example.habits9.ui.createyesnohabit  SwitchDefaults 'com.example.habits9.ui.createyesnohabit  Text 'com.example.habits9.ui.createyesnohabit  
TextButton 'com.example.habits9.ui.createyesnohabit  TextPrimary 'com.example.habits9.ui.createyesnohabit  
TextSecondary 'com.example.habits9.ui.createyesnohabit  
TimePicker 'com.example.habits9.ui.createyesnohabit  TimePickerDefaults 'com.example.habits9.ui.createyesnohabit  TimePickerDialog 'com.example.habits9.ui.createyesnohabit  	TopAppBar 'com.example.habits9.ui.createyesnohabit  TopAppBarDefaults 'com.example.habits9.ui.createyesnohabit  Unit 'com.example.habits9.ui.createyesnohabit  
background 'com.example.habits9.ui.createyesnohabit  	clickable 'com.example.habits9.ui.createyesnohabit  collectAsState 'com.example.habits9.ui.createyesnohabit  colors 'com.example.habits9.ui.createyesnohabit  com 'com.example.habits9.ui.createyesnohabit  fillMaxSize 'com.example.habits9.ui.createyesnohabit  fillMaxWidth 'com.example.habits9.ui.createyesnohabit  forEach 'com.example.habits9.ui.createyesnohabit  format 'com.example.habits9.ui.createyesnohabit  getValue 'com.example.habits9.ui.createyesnohabit  height 'com.example.habits9.ui.createyesnohabit  
isNotEmpty 'com.example.habits9.ui.createyesnohabit  kotlinx 'com.example.habits9.ui.createyesnohabit  listOf 'com.example.habits9.ui.createyesnohabit  minus 'com.example.habits9.ui.createyesnohabit  mutableStateOf 'com.example.habits9.ui.createyesnohabit  padding 'com.example.habits9.ui.createyesnohabit  plus 'com.example.habits9.ui.createyesnohabit  provideDelegate 'com.example.habits9.ui.createyesnohabit  remember 'com.example.habits9.ui.createyesnohabit  rememberTimePickerState 'com.example.habits9.ui.createyesnohabit  setValue 'com.example.habits9.ui.createyesnohabit  size 'com.example.habits9.ui.createyesnohabit  to 'com.example.habits9.ui.createyesnohabit  topAppBarColors 'com.example.habits9.ui.createyesnohabit  weight 'com.example.habits9.ui.createyesnohabit  width 'com.example.habits9.ui.createyesnohabit  example +com.example.habits9.ui.createyesnohabit.com  habits9 3com.example.habits9.ui.createyesnohabit.com.example  data ;com.example.habits9.ui.createyesnohabit.com.example.habits9  HabitSection @com.example.habits9.ui.createyesnohabit.com.example.habits9.data  	Alignment com.example.habits9.ui.details  Arrangement com.example.habits9.ui.details  Boolean com.example.habits9.ui.details  Build com.example.habits9.ui.details  CircularProgressIndicator com.example.habits9.ui.details  
Composable com.example.habits9.ui.details  DateTimeFormatter com.example.habits9.ui.details  	DayOfWeek com.example.habits9.ui.details  DropdownMenu com.example.habits9.ui.details  DropdownMenuItem com.example.habits9.ui.details  	Exception com.example.habits9.ui.details  ExperimentalMaterial3Api com.example.habits9.ui.details  Float com.example.habits9.ui.details  
FontFamily com.example.habits9.ui.details  
FontWeight com.example.habits9.ui.details  
FrequencyType com.example.habits9.ui.details  GeneralInfo com.example.habits9.ui.details  Habit com.example.habits9.ui.details  HabitAnalyticsUiState com.example.habits9.ui.details  HabitAnalyticsUseCase com.example.habits9.ui.details  HabitDetailsScreen com.example.habits9.ui.details  HabitDetailsUiState com.example.habits9.ui.details  HabitDetailsViewModel com.example.habits9.ui.details  HabitRepository com.example.habits9.ui.details  	HabitType com.example.habits9.ui.details  
HiltViewModel com.example.habits9.ui.details  Icon com.example.habits9.ui.details  
IconButton com.example.habits9.ui.details  Icons com.example.habits9.ui.details  Inject com.example.habits9.ui.details  Int com.example.habits9.ui.details  	LocalDate com.example.habits9.ui.details  Locale com.example.habits9.ui.details  Long com.example.habits9.ui.details  
MainViewModel com.example.habits9.ui.details  
MaterialTheme com.example.habits9.ui.details  MeasurableHabitAnalytics com.example.habits9.ui.details  
MetricView com.example.habits9.ui.details  Modifier com.example.habits9.ui.details  MutableStateFlow com.example.habits9.ui.details  OptIn com.example.habits9.ui.details  OverviewSection com.example.habits9.ui.details  RequiresApi com.example.habits9.ui.details  Row com.example.habits9.ui.details  Spacer com.example.habits9.ui.details  	StateFlow com.example.habits9.ui.details  String com.example.habits9.ui.details  SubHeaderRow com.example.habits9.ui.details  Text com.example.habits9.ui.details  	TextAlign com.example.habits9.ui.details  
TimePeriod com.example.habits9.ui.details  Toast com.example.habits9.ui.details  TopAppBarDefaults com.example.habits9.ui.details  Unit com.example.habits9.ui.details  UserPreferencesRepository com.example.habits9.ui.details  	ViewModel com.example.habits9.ui.details  YesNoHabitAnalytics com.example.habits9.ui.details  _uiState com.example.habits9.ui.details  analyticsUseCase com.example.habits9.ui.details  asStateFlow com.example.habits9.ui.details  
component1 com.example.habits9.ui.details  
component2 com.example.habits9.ui.details  fillMaxSize com.example.habits9.ui.details  fillMaxWidth com.example.habits9.ui.details  filter com.example.habits9.ui.details  first com.example.habits9.ui.details  formatCurrentDate com.example.habits9.ui.details  formatFrequencyText com.example.habits9.ui.details  	fromValue com.example.habits9.ui.details  
getWeekNumber com.example.habits9.ui.details  habitRepository com.example.habits9.ui.details  height com.example.habits9.ui.details  ifEmpty com.example.habits9.ui.details  
isNotEmpty com.example.habits9.ui.details  joinToString com.example.habits9.ui.details  launch com.example.habits9.ui.details  let com.example.habits9.ui.details  loadMeasurableHabitAnalytics com.example.habits9.ui.details  loadYesNoHabitAnalytics com.example.habits9.ui.details  map com.example.habits9.ui.details  	mapValues com.example.habits9.ui.details  provideDelegate com.example.habits9.ui.details  size com.example.habits9.ui.details  topAppBarColors com.example.habits9.ui.details  userPreferencesRepository com.example.habits9.ui.details  weight com.example.habits9.ui.details  copy 2com.example.habits9.ui.details.HabitDetailsUiState  
formattedDate 2com.example.habits9.ui.details.HabitDetailsUiState  
frequencyText 2com.example.habits9.ui.details.HabitDetailsUiState  habit 2com.example.habits9.ui.details.HabitDetailsUiState  measurableAnalytics 2com.example.habits9.ui.details.HabitDetailsUiState  
weekNumber 2com.example.habits9.ui.details.HabitDetailsUiState  yesNoAnalytics 2com.example.habits9.ui.details.HabitDetailsUiState  DateTimeFormatter 4com.example.habits9.ui.details.HabitDetailsViewModel  	DayOfWeek 4com.example.habits9.ui.details.HabitDetailsViewModel  
FrequencyType 4com.example.habits9.ui.details.HabitDetailsViewModel  HabitDetailsUiState 4com.example.habits9.ui.details.HabitDetailsViewModel  	HabitType 4com.example.habits9.ui.details.HabitDetailsViewModel  	LocalDate 4com.example.habits9.ui.details.HabitDetailsViewModel  Locale 4com.example.habits9.ui.details.HabitDetailsViewModel  
MainViewModel 4com.example.habits9.ui.details.HabitDetailsViewModel  MeasurableHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  MutableStateFlow 4com.example.habits9.ui.details.HabitDetailsViewModel  
TimePeriod 4com.example.habits9.ui.details.HabitDetailsViewModel  YesNoHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  _generalInfo 4com.example.habits9.ui.details.HabitDetailsViewModel  _uiState 4com.example.habits9.ui.details.HabitDetailsViewModel  analyticsUseCase 4com.example.habits9.ui.details.HabitDetailsViewModel  asStateFlow 4com.example.habits9.ui.details.HabitDetailsViewModel  
component1 4com.example.habits9.ui.details.HabitDetailsViewModel  
component2 4com.example.habits9.ui.details.HabitDetailsViewModel  filter 4com.example.habits9.ui.details.HabitDetailsViewModel  first 4com.example.habits9.ui.details.HabitDetailsViewModel  formatCurrentDate 4com.example.habits9.ui.details.HabitDetailsViewModel  formatFrequencyText 4com.example.habits9.ui.details.HabitDetailsViewModel  	fromValue 4com.example.habits9.ui.details.HabitDetailsViewModel  
getWeekNumber 4com.example.habits9.ui.details.HabitDetailsViewModel  habitRepository 4com.example.habits9.ui.details.HabitDetailsViewModel  
isNotEmpty 4com.example.habits9.ui.details.HabitDetailsViewModel  joinToString 4com.example.habits9.ui.details.HabitDetailsViewModel  launch 4com.example.habits9.ui.details.HabitDetailsViewModel  let 4com.example.habits9.ui.details.HabitDetailsViewModel  loadGeneralInfo 4com.example.habits9.ui.details.HabitDetailsViewModel  loadHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  loadHabitDetails 4com.example.habits9.ui.details.HabitDetailsViewModel  loadMeasurableHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  loadYesNoHabitAnalytics 4com.example.habits9.ui.details.HabitDetailsViewModel  map 4com.example.habits9.ui.details.HabitDetailsViewModel  	mapValues 4com.example.habits9.ui.details.HabitDetailsViewModel  uiState 4com.example.habits9.ui.details.HabitDetailsViewModel  userPreferencesRepository 4com.example.habits9.ui.details.HabitDetailsViewModel  viewModelScope 4com.example.habits9.ui.details.HabitDetailsViewModel  	Alignment #com.example.habits9.ui.habitreorder  Arrangement #com.example.habits9.ui.habitreorder  Boolean #com.example.habits9.ui.habitreorder  Box #com.example.habits9.ui.habitreorder  Card #com.example.habits9.ui.habitreorder  CardDefaults #com.example.habits9.ui.habitreorder  Column #com.example.habits9.ui.habitreorder  
Composable #com.example.habits9.ui.habitreorder  ExperimentalMaterial3Api #com.example.habits9.ui.habitreorder  Float #com.example.habits9.ui.habitreorder  
FontWeight #com.example.habits9.ui.habitreorder  Habit #com.example.habits9.ui.habitreorder  HabitReorderItem #com.example.habits9.ui.habitreorder  HabitReorderScreen #com.example.habits9.ui.habitreorder  HabitReorderUiState #com.example.habits9.ui.habitreorder  HabitReorderViewModel #com.example.habits9.ui.habitreorder  HabitRepository #com.example.habits9.ui.habitreorder  
HabitSortType #com.example.habits9.ui.habitreorder  HapticFeedbackType #com.example.habits9.ui.habitreorder  
HiltViewModel #com.example.habits9.ui.habitreorder  Icon #com.example.habits9.ui.habitreorder  
IconButton #com.example.habits9.ui.habitreorder  Icons #com.example.habits9.ui.habitreorder  Inject #com.example.habits9.ui.habitreorder  Int #com.example.habits9.ui.habitreorder  
LazyColumn #com.example.habits9.ui.habitreorder  List #com.example.habits9.ui.habitreorder  
MaterialTheme #com.example.habits9.ui.habitreorder  Modifier #com.example.habits9.ui.habitreorder  MutableStateFlow #com.example.habits9.ui.habitreorder  Offset #com.example.habits9.ui.habitreorder  OptIn #com.example.habits9.ui.habitreorder  Row #com.example.habits9.ui.habitreorder  Scaffold #com.example.habits9.ui.habitreorder  Spacer #com.example.habits9.ui.habitreorder  Spring #com.example.habits9.ui.habitreorder  	StateFlow #com.example.habits9.ui.habitreorder  String #com.example.habits9.ui.habitreorder  Text #com.example.habits9.ui.habitreorder  	TopAppBar #com.example.habits9.ui.habitreorder  TopAppBarDefaults #com.example.habits9.ui.habitreorder  Unit #com.example.habits9.ui.habitreorder  UserPreferencesRepository #com.example.habits9.ui.habitreorder  	ViewModel #com.example.habits9.ui.habitreorder  _uiState #com.example.habits9.ui.habitreorder  android #com.example.habits9.ui.habitreorder  androidx #com.example.habits9.ui.habitreorder  animateFloatAsState #com.example.habits9.ui.habitreorder  asStateFlow #com.example.habits9.ui.habitreorder  
cardColors #com.example.habits9.ui.habitreorder  collectAsState #com.example.habits9.ui.habitreorder  	compareBy #com.example.habits9.ui.habitreorder  	emptyList #com.example.habits9.ui.habitreorder  fillMaxSize #com.example.habits9.ui.habitreorder  fillMaxWidth #com.example.habits9.ui.habitreorder  find #com.example.habits9.ui.habitreorder  getValue #com.example.habits9.ui.habitreorder  habitRepository #com.example.habits9.ui.habitreorder  height #com.example.habits9.ui.habitreorder  kotlinx #com.example.habits9.ui.habitreorder  launch #com.example.habits9.ui.habitreorder  map #com.example.habits9.ui.habitreorder  
mapIndexed #com.example.habits9.ui.habitreorder  mutableStateOf #com.example.habits9.ui.habitreorder  padding #com.example.habits9.ui.habitreorder  
plusAssign #com.example.habits9.ui.habitreorder  provideDelegate #com.example.habits9.ui.habitreorder  remember #com.example.habits9.ui.habitreorder  setValue #com.example.habits9.ui.habitreorder  
sortedWith #com.example.habits9.ui.habitreorder  spacedBy #com.example.habits9.ui.habitreorder  thenBy #com.example.habits9.ui.habitreorder  to #com.example.habits9.ui.habitreorder  toMap #com.example.habits9.ui.habitreorder  
toMutableList #com.example.habits9.ui.habitreorder  topAppBarColors #com.example.habits9.ui.habitreorder  tween #com.example.habits9.ui.habitreorder  userPreferencesRepository #com.example.habits9.ui.habitreorder  weight #com.example.habits9.ui.habitreorder  copy 7com.example.habits9.ui.habitreorder.HabitReorderUiState  habits 7com.example.habits9.ui.habitreorder.HabitReorderUiState  isLocallyReordering 7com.example.habits9.ui.habitreorder.HabitReorderUiState  HabitReorderUiState 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  
HabitSortType 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  Int 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  MutableStateFlow 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  _uiState 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  android 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  asStateFlow 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  	compareBy 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  forceRefresh 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  habitRepository 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  launch 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  map 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  
mapIndexed 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  moveHabitAndSave 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  
sortedWith 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  thenBy 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  to 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  toMap 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  
toMutableList 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  uiState 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  userPreferencesRepository 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  viewModelScope 9com.example.habits9.ui.habitreorder.HabitReorderViewModel  compose ,com.example.habits9.ui.habitreorder.androidx  
foundation 4com.example.habits9.ui.habitreorder.androidx.compose  lazy ?com.example.habits9.ui.habitreorder.androidx.compose.foundation  LazyListItemInfo Dcom.example.habits9.ui.habitreorder.androidx.compose.foundation.lazy  
AccentPrimary )com.example.habits9.ui.habittypeselection  	Alignment )com.example.habits9.ui.habittypeselection  Arrangement )com.example.habits9.ui.habittypeselection  Card )com.example.habits9.ui.habittypeselection  CardDefaults )com.example.habits9.ui.habittypeselection  Column )com.example.habits9.ui.habittypeselection  
Composable )com.example.habits9.ui.habittypeselection  DarkBackground )com.example.habits9.ui.habittypeselection  ExperimentalMaterial3Api )com.example.habits9.ui.habittypeselection  
FontWeight )com.example.habits9.ui.habittypeselection  HabitTypeSelectionScreen )com.example.habits9.ui.habittypeselection  Icon )com.example.habits9.ui.habittypeselection  
IconButton )com.example.habits9.ui.habittypeselection  Icons )com.example.habits9.ui.habittypeselection  Modifier )com.example.habits9.ui.habittypeselection  OptIn )com.example.habits9.ui.habittypeselection  RoundedCornerShape )com.example.habits9.ui.habittypeselection  Scaffold )com.example.habits9.ui.habittypeselection  Spacer )com.example.habits9.ui.habittypeselection  SurfaceVariantDark )com.example.habits9.ui.habittypeselection  Text )com.example.habits9.ui.habittypeselection  TextPrimary )com.example.habits9.ui.habittypeselection  
TextSecondary )com.example.habits9.ui.habittypeselection  	TopAppBar )com.example.habits9.ui.habittypeselection  TopAppBarDefaults )com.example.habits9.ui.habittypeselection  Unit )com.example.habits9.ui.habittypeselection  
cardColors )com.example.habits9.ui.habittypeselection  	clickable )com.example.habits9.ui.habittypeselection  fillMaxSize )com.example.habits9.ui.habittypeselection  fillMaxWidth )com.example.habits9.ui.habittypeselection  height )com.example.habits9.ui.habittypeselection  padding )com.example.habits9.ui.habittypeselection  topAppBarColors )com.example.habits9.ui.habittypeselection  
AccentPrimary com.example.habits9.ui.home  	Alignment com.example.habits9.ui.home  Arrangement com.example.habits9.ui.home  BackgroundDark com.example.habits9.ui.home  Boolean com.example.habits9.ui.home  Box com.example.habits9.ui.home  Build com.example.habits9.ui.home  CircleShape com.example.habits9.ui.home  Color com.example.habits9.ui.home  Column com.example.habits9.ui.home  CompletionIndicator com.example.habits9.ui.home  
Composable com.example.habits9.ui.home  CustomHeader com.example.habits9.ui.home  	DayOfWeek com.example.habits9.ui.home  DividerColor com.example.habits9.ui.home  DropdownMenu com.example.habits9.ui.home  DropdownMenuItem com.example.habits9.ui.home  ExperimentalMaterial3Api com.example.habits9.ui.home  
FontFamily com.example.habits9.ui.home  
FontWeight com.example.habits9.ui.home  FrozenPaneLayout com.example.habits9.ui.home  
HomeScreen com.example.habits9.ui.home  Icon com.example.habits9.ui.home  
IconButton com.example.habits9.ui.home  Icons com.example.habits9.ui.home  
LazyColumn com.example.habits9.ui.home  List com.example.habits9.ui.home  	LocalDate com.example.habits9.ui.home  Long com.example.habits9.ui.home  Modifier com.example.habits9.ui.home  Offset com.example.habits9.ui.home  OptIn com.example.habits9.ui.home  RequiresApi com.example.habits9.ui.home  Row com.example.habits9.ui.home  SortMenuButton com.example.habits9.ui.home  Spacer com.example.habits9.ui.home  String com.example.habits9.ui.home  Stroke com.example.habits9.ui.home  SurfaceVariantDark com.example.habits9.ui.home  Text com.example.habits9.ui.home  	TextAlign com.example.habits9.ui.home  TextPrimary com.example.habits9.ui.home  
TextSecondary com.example.habits9.ui.home  TopAppBarDefaults com.example.habits9.ui.home  Unit com.example.habits9.ui.home  alpha com.example.habits9.ui.home  android com.example.habits9.ui.home  
background com.example.habits9.ui.home  	clickable com.example.habits9.ui.home  com com.example.habits9.ui.home  
drawBehind com.example.habits9.ui.home  fillMaxSize com.example.habits9.ui.home  fillMaxWidth com.example.habits9.ui.home  forEachIndexed com.example.habits9.ui.home  height com.example.habits9.ui.home  horizontalScroll com.example.habits9.ui.home  
isNotEmpty com.example.habits9.ui.home  
isNullOrEmpty com.example.habits9.ui.home  isWeekStartDay com.example.habits9.ui.home  padding com.example.habits9.ui.home  provideDelegate com.example.habits9.ui.home  size com.example.habits9.ui.home  spacedBy com.example.habits9.ui.home  take com.example.habits9.ui.home  toDoubleOrNull com.example.habits9.ui.home  topAppBarColors com.example.habits9.ui.home  weight com.example.habits9.ui.home  width com.example.habits9.ui.home  example com.example.habits9.ui.home.com  habits9 'com.example.habits9.ui.home.com.example  data /com.example.habits9.ui.home.com.example.habits9  ui /com.example.habits9.ui.home.com.example.habits9  
HabitSortType 4com.example.habits9.ui.home.com.example.habits9.data  	HabitType 4com.example.habits9.ui.home.com.example.habits9.data  HabitWithCompletions 2com.example.habits9.ui.home.com.example.habits9.ui  
MainViewModel 2com.example.habits9.ui.home.com.example.habits9.ui  WeekInfo 2com.example.habits9.ui.home.com.example.habits9.ui  
AccentPrimary %com.example.habits9.ui.managesections  AlertDialog %com.example.habits9.ui.managesections  	Alignment %com.example.habits9.ui.managesections  Arrangement %com.example.habits9.ui.managesections  Boolean %com.example.habits9.ui.managesections  Box %com.example.habits9.ui.managesections  Card %com.example.habits9.ui.managesections  CardDefaults %com.example.habits9.ui.managesections  CircleShape %com.example.habits9.ui.managesections  CircularProgressIndicator %com.example.habits9.ui.managesections  Color %com.example.habits9.ui.managesections  Column %com.example.habits9.ui.managesections  
Composable %com.example.habits9.ui.managesections  CreateSectionDialog %com.example.habits9.ui.managesections  DarkBackground %com.example.habits9.ui.managesections  DeleteSectionDialog %com.example.habits9.ui.managesections  DividerColor %com.example.habits9.ui.managesections  EditSectionDialog %com.example.habits9.ui.managesections  ExperimentalMaterial3Api %com.example.habits9.ui.managesections  
FontWeight %com.example.habits9.ui.managesections  HabitSection %com.example.habits9.ui.managesections  HabitSectionRepository %com.example.habits9.ui.managesections  HapticFeedbackType %com.example.habits9.ui.managesections  
HiltViewModel %com.example.habits9.ui.managesections  Icon %com.example.habits9.ui.managesections  
IconButton %com.example.habits9.ui.managesections  Icons %com.example.habits9.ui.managesections  Inject %com.example.habits9.ui.managesections  Int %com.example.habits9.ui.managesections  
LazyColumn %com.example.habits9.ui.managesections  
LazyListState %com.example.habits9.ui.managesections  LazyRow %com.example.habits9.ui.managesections  List %com.example.habits9.ui.managesections  ManageSectionsScreen %com.example.habits9.ui.managesections  ManageSectionsUiState %com.example.habits9.ui.managesections  ManageSectionsViewModel %com.example.habits9.ui.managesections  Modifier %com.example.habits9.ui.managesections  MutableStateFlow %com.example.habits9.ui.managesections  Offset %com.example.habits9.ui.managesections  OptIn %com.example.habits9.ui.managesections  OutlinedTextField %com.example.habits9.ui.managesections  OutlinedTextFieldDefaults %com.example.habits9.ui.managesections  
PaddingValues %com.example.habits9.ui.managesections  RoundedCornerShape %com.example.habits9.ui.managesections  Row %com.example.habits9.ui.managesections  Scaffold %com.example.habits9.ui.managesections  
SectionColors %com.example.habits9.ui.managesections  SectionListItem %com.example.habits9.ui.managesections  Spacer %com.example.habits9.ui.managesections  	StateFlow %com.example.habits9.ui.managesections  String %com.example.habits9.ui.managesections  SurfaceVariantDark %com.example.habits9.ui.managesections  Text %com.example.habits9.ui.managesections  
TextButton %com.example.habits9.ui.managesections  TextPrimary %com.example.habits9.ui.managesections  
TextSecondary %com.example.habits9.ui.managesections  	TopAppBar %com.example.habits9.ui.managesections  TopAppBarDefaults %com.example.habits9.ui.managesections  Unit %com.example.habits9.ui.managesections  	ViewModel %com.example.habits9.ui.managesections  _uiState %com.example.habits9.ui.managesections  align %com.example.habits9.ui.managesections  alpha %com.example.habits9.ui.managesections  asStateFlow %com.example.habits9.ui.managesections  
background %com.example.habits9.ui.managesections  
cardColors %com.example.habits9.ui.managesections  	clickable %com.example.habits9.ui.managesections  collectAsState %com.example.habits9.ui.managesections  colors %com.example.habits9.ui.managesections  	emptyList %com.example.habits9.ui.managesections  fillMaxSize %com.example.habits9.ui.managesections  fillMaxWidth %com.example.habits9.ui.managesections  find %com.example.habits9.ui.managesections  findSectionIndex %com.example.habits9.ui.managesections  getValue %com.example.habits9.ui.managesections  
graphicsLayer %com.example.habits9.ui.managesections  habitSectionRepository %com.example.habits9.ui.managesections  height %com.example.habits9.ui.managesections  hideCreateDialog %com.example.habits9.ui.managesections  hideDeleteDialog %com.example.habits9.ui.managesections  hideEditDialog %com.example.habits9.ui.managesections  indexOfFirst %com.example.habits9.ui.managesections  isBlank %com.example.habits9.ui.managesections  
isNotBlank %com.example.habits9.ui.managesections  launch %com.example.habits9.ui.managesections  let %com.example.habits9.ui.managesections  listOf %com.example.habits9.ui.managesections  
mapIndexed %com.example.habits9.ui.managesections  mutableStateOf %com.example.habits9.ui.managesections  padding %com.example.habits9.ui.managesections  
plusAssign %com.example.habits9.ui.managesections  pointerInput %com.example.habits9.ui.managesections  provideDelegate %com.example.habits9.ui.managesections  remember %com.example.habits9.ui.managesections  rememberCoroutineScope %com.example.habits9.ui.managesections  setValue %com.example.habits9.ui.managesections  shadow %com.example.habits9.ui.managesections  size %com.example.habits9.ui.managesections  spacedBy %com.example.habits9.ui.managesections  takeIf %com.example.habits9.ui.managesections  
toMutableList %com.example.habits9.ui.managesections  topAppBarColors %com.example.habits9.ui.managesections  trim %com.example.habits9.ui.managesections  weight %com.example.habits9.ui.managesections  width %com.example.habits9.ui.managesections  zIndex %com.example.habits9.ui.managesections  copy ;com.example.habits9.ui.managesections.ManageSectionsUiState  deletingSection ;com.example.habits9.ui.managesections.ManageSectionsUiState  editingSection ;com.example.habits9.ui.managesections.ManageSectionsUiState  	isLoading ;com.example.habits9.ui.managesections.ManageSectionsUiState  sections ;com.example.habits9.ui.managesections.ManageSectionsUiState  showCreateDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  showDeleteDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  showEditDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  HabitSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  ManageSectionsUiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  MutableStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  _uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  asStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  
createSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  
deleteSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  habitSectionRepository =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideDeleteDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideEditDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  isBlank =com.example.habits9.ui.managesections.ManageSectionsViewModel  launch =com.example.habits9.ui.managesections.ManageSectionsViewModel  loadSections =com.example.habits9.ui.managesections.ManageSectionsViewModel  
mapIndexed =com.example.habits9.ui.managesections.ManageSectionsViewModel  onSectionMoved =com.example.habits9.ui.managesections.ManageSectionsViewModel  showCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  showDeleteDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  showEditDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  
toMutableList =com.example.habits9.ui.managesections.ManageSectionsViewModel  trim =com.example.habits9.ui.managesections.ManageSectionsViewModel  uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  
updateSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  viewModelScope =com.example.habits9.ui.managesections.ManageSectionsViewModel  
AccentPrimary com.example.habits9.ui.settings  	Alignment com.example.habits9.ui.settings  Boolean com.example.habits9.ui.settings  Button com.example.habits9.ui.settings  ButtonDefaults com.example.habits9.ui.settings  CircularProgressIndicator com.example.habits9.ui.settings  Color com.example.habits9.ui.settings  Column com.example.habits9.ui.settings  
Composable com.example.habits9.ui.settings  DarkBackground com.example.habits9.ui.settings  EmailAuthProvider com.example.habits9.ui.settings  ExperimentalMaterial3Api com.example.habits9.ui.settings  FirebaseAuth com.example.habits9.ui.settings  (FirebaseAuthRecentLoginRequiredException com.example.habits9.ui.settings  
FontFamily com.example.habits9.ui.settings  
FontWeight com.example.habits9.ui.settings  
HiltViewModel com.example.habits9.ui.settings  Icon com.example.habits9.ui.settings  Icons com.example.habits9.ui.settings  Inject com.example.habits9.ui.settings  KeyboardOptions com.example.habits9.ui.settings  KeyboardType com.example.habits9.ui.settings  Modifier com.example.habits9.ui.settings  OptIn com.example.habits9.ui.settings  OutlinedTextField com.example.habits9.ui.settings  OutlinedTextFieldDefaults com.example.habits9.ui.settings  PasswordVisualTransformation com.example.habits9.ui.settings  RadioButton com.example.habits9.ui.settings  RadioButtonDefaults com.example.habits9.ui.settings  ReauthenticationDialog com.example.habits9.ui.settings  Role com.example.habits9.ui.settings  RoundedCornerShape com.example.habits9.ui.settings  Row com.example.habits9.ui.settings  SettingsScreen com.example.habits9.ui.settings  SettingsViewModel com.example.habits9.ui.settings  SharingStarted com.example.habits9.ui.settings  Spacer com.example.habits9.ui.settings  	StateFlow com.example.habits9.ui.settings  String com.example.habits9.ui.settings  SurfaceVariantDark com.example.habits9.ui.settings  Text com.example.habits9.ui.settings  TextPrimary com.example.habits9.ui.settings  
TextSecondary com.example.habits9.ui.settings  TopAppBarDefaults com.example.habits9.ui.settings  Unit com.example.habits9.ui.settings  UserPreferencesRepository com.example.habits9.ui.settings  	ViewModel com.example.habits9.ui.settings  WhileSubscribed com.example.habits9.ui.settings  androidx com.example.habits9.ui.settings  buttonColors com.example.habits9.ui.settings  clip com.example.habits9.ui.settings  colors com.example.habits9.ui.settings  
deleteAccount com.example.habits9.ui.settings  fillMaxSize com.example.habits9.ui.settings  fillMaxWidth com.example.habits9.ui.settings  height com.example.habits9.ui.settings  
isNotBlank com.example.habits9.ui.settings  launch com.example.habits9.ui.settings  padding com.example.habits9.ui.settings  provideDelegate com.example.habits9.ui.settings  reauthenticateAndDelete com.example.habits9.ui.settings  
selectable com.example.habits9.ui.settings  selectableGroup com.example.habits9.ui.settings  size com.example.habits9.ui.settings  stateIn com.example.habits9.ui.settings  topAppBarColors com.example.habits9.ui.settings  userPreferencesRepository com.example.habits9.ui.settings  width com.example.habits9.ui.settings  SharingStarted 1com.example.habits9.ui.settings.SettingsViewModel  WhileSubscribed 1com.example.habits9.ui.settings.SettingsViewModel  firstDayOfWeek 1com.example.habits9.ui.settings.SettingsViewModel  launch 1com.example.habits9.ui.settings.SettingsViewModel  stateIn 1com.example.habits9.ui.settings.SettingsViewModel  updateFirstDayOfWeek 1com.example.habits9.ui.settings.SettingsViewModel  userPreferencesRepository 1com.example.habits9.ui.settings.SettingsViewModel  viewModelScope 1com.example.habits9.ui.settings.SettingsViewModel  Boolean com.example.habits9.utils  
ChronoUnit com.example.habits9.utils  	DayOfWeek com.example.habits9.utils  EnhancedFrequency com.example.habits9.utils  
FrequencyType com.example.habits9.utils  Habit com.example.habits9.utils  HabitScheduler com.example.habits9.utils  IllegalArgumentException com.example.habits9.utils  Int com.example.habits9.utils  	LocalDate com.example.habits9.utils  Long com.example.habits9.utils  TemporalAdjusters com.example.habits9.utils  fromDatabaseValues com.example.habits9.utils  java com.example.habits9.utils  toList com.example.habits9.utils  
ChronoUnit (com.example.habits9.utils.HabitScheduler  	DayOfWeek (com.example.habits9.utils.HabitScheduler  EnhancedFrequency (com.example.habits9.utils.HabitScheduler  
FrequencyType (com.example.habits9.utils.HabitScheduler  IllegalArgumentException (com.example.habits9.utils.HabitScheduler  	LocalDate (com.example.habits9.utils.HabitScheduler  TemporalAdjusters (com.example.habits9.utils.HabitScheduler  fromDatabaseValues (com.example.habits9.utils.HabitScheduler  isDailyHabitScheduled (com.example.habits9.utils.HabitScheduler  isHabitScheduled (com.example.habits9.utils.HabitScheduler  isMonthlyHabitScheduled (com.example.habits9.utils.HabitScheduler  isScheduledWithFrequency (com.example.habits9.utils.HabitScheduler  isSpecificWeekdayInMonth (com.example.habits9.utils.HabitScheduler  isWeeklyHabitScheduled (com.example.habits9.utils.HabitScheduler  java (com.example.habits9.utils.HabitScheduler  toList (com.example.habits9.utils.HabitScheduler  AndroidEntryPoint com.example.uhabits_99  
AuthScreen com.example.uhabits_99  Build com.example.uhabits_99  Bundle com.example.uhabits_99  ComponentActivity com.example.uhabits_99  CreateMeasurableHabitScreen com.example.uhabits_99  CreateYesNoHabitScreen com.example.uhabits_99  FirebaseAuth com.example.uhabits_99  HabitDetailsScreen com.example.uhabits_99  HabitReorderScreen com.example.uhabits_99  HabitTypeSelectionScreen com.example.uhabits_99  
HomeScreen com.example.uhabits_99  MainActivity com.example.uhabits_99  ManageSectionsScreen com.example.uhabits_99  NavHost com.example.uhabits_99  RequiresApi com.example.uhabits_99  SettingsScreen com.example.uhabits_99  UHabits_99Theme com.example.uhabits_99  VerificationScreen com.example.uhabits_99  rememberNavController com.example.uhabits_99  toLongOrNull com.example.uhabits_99  
AuthScreen #com.example.uhabits_99.MainActivity  Build #com.example.uhabits_99.MainActivity  CreateMeasurableHabitScreen #com.example.uhabits_99.MainActivity  CreateYesNoHabitScreen #com.example.uhabits_99.MainActivity  FirebaseAuth #com.example.uhabits_99.MainActivity  HabitDetailsScreen #com.example.uhabits_99.MainActivity  HabitReorderScreen #com.example.uhabits_99.MainActivity  HabitTypeSelectionScreen #com.example.uhabits_99.MainActivity  
HomeScreen #com.example.uhabits_99.MainActivity  ManageSectionsScreen #com.example.uhabits_99.MainActivity  NavHost #com.example.uhabits_99.MainActivity  SettingsScreen #com.example.uhabits_99.MainActivity  UHabits_99Theme #com.example.uhabits_99.MainActivity  VerificationScreen #com.example.uhabits_99.MainActivity  
composable #com.example.uhabits_99.MainActivity  rememberNavController #com.example.uhabits_99.MainActivity  
setContent #com.example.uhabits_99.MainActivity  toLongOrNull #com.example.uhabits_99.MainActivity  Boolean com.example.uhabits_99.ui.theme  Build com.example.uhabits_99.ui.theme  
Composable com.example.uhabits_99.ui.theme  DarkAccentPrimary com.example.uhabits_99.ui.theme  DarkBackground com.example.uhabits_99.ui.theme  DarkColorScheme com.example.uhabits_99.ui.theme  DarkDivider com.example.uhabits_99.ui.theme  DarkSurfaceVariant com.example.uhabits_99.ui.theme  DarkTextPrimary com.example.uhabits_99.ui.theme  DarkTextSecondary com.example.uhabits_99.ui.theme  
FontFamily com.example.uhabits_99.ui.theme  
FontWeight com.example.uhabits_99.ui.theme  LightAccentPrimary com.example.uhabits_99.ui.theme  LightBackground com.example.uhabits_99.ui.theme  LightColorScheme com.example.uhabits_99.ui.theme  LightDivider com.example.uhabits_99.ui.theme  LightSurfaceVariant com.example.uhabits_99.ui.theme  LightTextPrimary com.example.uhabits_99.ui.theme  LightTextSecondary com.example.uhabits_99.ui.theme  Pink40 com.example.uhabits_99.ui.theme  Pink80 com.example.uhabits_99.ui.theme  Purple40 com.example.uhabits_99.ui.theme  Purple80 com.example.uhabits_99.ui.theme  PurpleGrey40 com.example.uhabits_99.ui.theme  PurpleGrey80 com.example.uhabits_99.ui.theme  
Typography com.example.uhabits_99.ui.theme  UHabits_99Theme com.example.uhabits_99.ui.theme  Unit com.example.uhabits_99.ui.theme  OnCompleteListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  addOnCompleteListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  	exception !com.google.android.gms.tasks.Task  isSuccessful !com.google.android.gms.tasks.Task  AuthCredential com.google.firebase.auth  EmailAuthProvider com.google.firebase.auth  FirebaseAuth com.google.firebase.auth  FirebaseAuthException com.google.firebase.auth  (FirebaseAuthRecentLoginRequiredException com.google.firebase.auth  FirebaseUser com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  
getCredential *com.google.firebase.auth.EmailAuthProvider  createUserWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  sendPasswordResetEmail %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  	errorCode .com.google.firebase.auth.FirebaseAuthException  message .com.google.firebase.auth.FirebaseAuthException  delete %com.google.firebase.auth.FirebaseUser  email %com.google.firebase.auth.FirebaseUser  isEmailVerified %com.google.firebase.auth.FirebaseUser  reauthenticate %com.google.firebase.auth.FirebaseUser  sendEmailVerification %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  isEmailVerified !com.google.firebase.auth.UserInfo  CollectionReference com.google.firebase.firestore  DocumentReference com.google.firebase.firestore  
EventListener com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  FirebaseFirestoreException com.google.firebase.firestore  ListenerRegistration com.google.firebase.firestore  Query com.google.firebase.firestore  
QuerySnapshot com.google.firebase.firestore  
WriteBatch com.google.firebase.firestore  add 1com.google.firebase.firestore.CollectionReference  addSnapshotListener 1com.google.firebase.firestore.CollectionReference  document 1com.google.firebase.firestore.CollectionReference  get 1com.google.firebase.firestore.CollectionReference  orderBy 1com.google.firebase.firestore.CollectionReference  whereEqualTo 1com.google.firebase.firestore.CollectionReference  
collection /com.google.firebase.firestore.DocumentReference  delete /com.google.firebase.firestore.DocumentReference  id /com.google.firebase.firestore.DocumentReference  set /com.google.firebase.firestore.DocumentReference  id .com.google.firebase.firestore.DocumentSnapshot  	reference .com.google.firebase.firestore.DocumentSnapshot  toObject .com.google.firebase.firestore.DocumentSnapshot  <SAM-CONSTRUCTOR> +com.google.firebase.firestore.EventListener  batch /com.google.firebase.firestore.FirebaseFirestore  
collection /com.google.firebase.firestore.FirebaseFirestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  remove 2com.google.firebase.firestore.ListenerRegistration  addSnapshotListener #com.google.firebase.firestore.Query  get #com.google.firebase.firestore.Query  orderBy #com.google.firebase.firestore.Query  whereEqualTo #com.google.firebase.firestore.Query  whereGreaterThanOrEqualTo #com.google.firebase.firestore.Query  whereLessThanOrEqualTo #com.google.firebase.firestore.Query  	documents +com.google.firebase.firestore.QuerySnapshot  isEmpty +com.google.firebase.firestore.QuerySnapshot  commit (com.google.firebase.firestore.WriteBatch  delete (com.google.firebase.firestore.WriteBatch  set (com.google.firebase.firestore.WriteBatch  update (com.google.firebase.firestore.WriteBatch  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  Void 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	DayOfWeek 	java.time  Instant 	java.time  	LocalDate 	java.time  	LocalTime 	java.time  ZoneId 	java.time  FRIDAY java.time.DayOfWeek  MONDAY java.time.DayOfWeek  SATURDAY java.time.DayOfWeek  SUNDAY java.time.DayOfWeek  THURSDAY java.time.DayOfWeek  TUESDAY java.time.DayOfWeek  	WEDNESDAY java.time.DayOfWeek  name java.time.DayOfWeek  to java.time.DayOfWeek  value java.time.DayOfWeek  atZone java.time.Instant  ofEpochMilli java.time.Instant  toEpochMilli java.time.Instant  atStartOfDay java.time.LocalDate  
dayOfMonth java.time.LocalDate  	dayOfWeek java.time.LocalDate  format java.time.LocalDate  get java.time.LocalDate  isAfter java.time.LocalDate  isBefore java.time.LocalDate  
lengthOfMonth java.time.LocalDate  	minusDays java.time.LocalDate  minusMonths java.time.LocalDate  
minusWeeks java.time.LocalDate  
minusYears java.time.LocalDate  month java.time.LocalDate  
monthValue java.time.LocalDate  now java.time.LocalDate  of java.time.LocalDate  
ofEpochDay java.time.LocalDate  plusDays java.time.LocalDate  
plusMonths java.time.LocalDate  	plusWeeks java.time.LocalDate  	plusYears java.time.LocalDate  until java.time.LocalDate  with java.time.LocalDate  withDayOfMonth java.time.LocalDate  year java.time.LocalDate  hour java.time.LocalTime  minute java.time.LocalTime  of java.time.LocalTime  days java.time.Period  
systemDefault java.time.ZoneId  	toInstant java.time.ZonedDateTime  toLocalDate java.time.ZonedDateTime  DateTimeFormatter java.time.format  	ofPattern "java.time.format.DateTimeFormatter  
ChronoUnit java.time.temporal  TemporalAdjusters java.time.temporal  
WeekFields java.time.temporal  DAYS java.time.temporal.ChronoUnit  MONTHS java.time.temporal.ChronoUnit  WEEKS java.time.temporal.ChronoUnit  between java.time.temporal.ChronoUnit  
nextOrSame $java.time.temporal.TemporalAdjusters  previousOrSame $java.time.temporal.TemporalAdjusters  of java.time.temporal.WeekFields  
weekOfYear java.time.temporal.WeekFields  
Comparator 	java.util  Locale 	java.util  NoSuchElementException 	java.util  UUID 	java.util  thenBy java.util.Comparator  
getDefault java.util.Locale  
randomUUID java.util.UUID  toString java.util.UUID  Inject javax.inject  	Singleton javax.inject  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  
Comparable kotlin  DoubleArray kotlin  Enum kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  IllegalArgumentException kotlin  Int kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  assert kotlin  let kotlin  map kotlin  minus kotlin  plus kotlin  takeIf kotlin  to kotlin  toList kotlin  hashCode 
kotlin.Any  contains kotlin.Array  find kotlin.Array  forEach kotlin.Array  toList kotlin.Array  toSet kotlin.Array  not kotlin.Boolean  	uppercase kotlin.Char  isEmpty kotlin.CharSequence  	compareTo 
kotlin.Double  div 
kotlin.Double  sp 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  toString 
kotlin.Double  AT_LEAST kotlin.Enum  AT_MOST kotlin.Enum  	Companion kotlin.Enum  DAILY kotlin.Enum  	DayOfWeek kotlin.Enum  
FrequencyType kotlin.Enum  	HabitType kotlin.Enum  IllegalStateException kotlin.Enum  Int kotlin.Enum  List kotlin.Enum  MONTHLY kotlin.Enum  	NUMERICAL kotlin.Enum  NumericalHabitType kotlin.Enum  String kotlin.Enum  WEEKLY kotlin.Enum  YES_NO kotlin.Enum  	emptyList kotlin.Enum  find kotlin.Enum  
isNullOrBlank kotlin.Enum  joinToString kotlin.Enum  map kotlin.Enum  
mapNotNull kotlin.Enum  split kotlin.Enum  toIntOrNull kotlin.Enum  values kotlin.Enum  AT_LEAST kotlin.Enum.Companion  AT_MOST kotlin.Enum.Companion  DAILY kotlin.Enum.Companion  IllegalStateException kotlin.Enum.Companion  MONTHLY kotlin.Enum.Companion  	NUMERICAL kotlin.Enum.Companion  WEEKLY kotlin.Enum.Companion  YES_NO kotlin.Enum.Companion  	emptyList kotlin.Enum.Companion  find kotlin.Enum.Companion  
isNullOrBlank kotlin.Enum.Companion  joinToString kotlin.Enum.Companion  map kotlin.Enum.Companion  
mapNotNull kotlin.Enum.Companion  split kotlin.Enum.Companion  toIntOrNull kotlin.Enum.Companion  values kotlin.Enum.Companion  message kotlin.Exception  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toDouble kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function3  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  takeIf 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  div kotlin.Long  let kotlin.Long  rem kotlin.Long  times kotlin.Long  to kotlin.Long  toInt kotlin.Long  toString kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  format 
kotlin.String  hashCode 
kotlin.String  ifEmpty 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  replace 
kotlin.String  replaceFirstChar 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toDoubleOrNull 
kotlin.String  toIntOrNull 
kotlin.String  toLongOrNull 
kotlin.String  trim 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  
Collection kotlin.collections  IndexedValue kotlin.collections  IntIterator kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  	associate kotlin.collections  associateBy kotlin.collections  
associateWith kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  distinct kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  find kotlin.collections  first kotlin.collections  firstNotNullOfOrNull kotlin.collections  flatten kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  groupBy kotlin.collections  ifEmpty kotlin.collections  indexOfFirst kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapIndexed kotlin.collections  mapKeys kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  	mapValues kotlin.collections  maxOf kotlin.collections  	maxOrNull kotlin.collections  minus kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  setOf kotlin.collections  sorted kotlin.collections  sortedBy kotlin.collections  
sortedWith kotlin.collections  sumOf kotlin.collections  sumOfDouble kotlin.collections  take kotlin.collections  toList kotlin.collections  toMap kotlin.collections  
toMutableList kotlin.collections  toSet kotlin.collections  	withIndex kotlin.collections  any kotlin.collections.Collection  average kotlin.collections.Collection  sumOf kotlin.collections.Collection  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  	associate kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  all kotlin.collections.List  any kotlin.collections.List  	associate kotlin.collections.List  associateBy kotlin.collections.List  
associateWith kotlin.collections.List  average kotlin.collections.List  contains kotlin.collections.List  count kotlin.collections.List  distinct kotlin.collections.List  filter kotlin.collections.List  find kotlin.collections.List  first kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  groupBy kotlin.collections.List  indexOfFirst kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  
mapIndexed kotlin.collections.List  
mapNotNull kotlin.collections.List  	maxOrNull kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  sorted kotlin.collections.List  sortedBy kotlin.collections.List  
sortedWith kotlin.collections.List  sumOf kotlin.collections.List  take kotlin.collections.List  toMap kotlin.collections.List  
toMutableList kotlin.collections.List  toSet kotlin.collections.List  	withIndex kotlin.collections.List  Entry kotlin.collections.Map  count kotlin.collections.Map  get kotlin.collections.Map  keys kotlin.collections.Map  mapKeys kotlin.collections.Map  	mapValues kotlin.collections.Map  size kotlin.collections.Map  toMap kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  flatten $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  iterator kotlin.collections.MutableList  
mapIndexed kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  get kotlin.collections.MutableMap  set kotlin.collections.MutableMap  values kotlin.collections.MutableMap  contains kotlin.collections.Set  first kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  minus kotlin.collections.Set  plus kotlin.collections.Set  size kotlin.collections.Set  sortedBy kotlin.collections.Set  toList kotlin.collections.Set  	compareBy kotlin.comparisons  maxOf kotlin.comparisons  thenBy kotlin.comparisons  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  SuspendFunction3 kotlin.coroutines  SuspendFunction5 kotlin.coroutines  
startsWith 	kotlin.io  java 
kotlin.jvm  kotlin 
kotlin.jvm  abs kotlin.math  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  map kotlin.ranges.IntRange  KClass kotlin.reflect  
KFunction1 kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  	associate kotlin.sequences  associateBy kotlin.sequences  
associateWith kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  distinct kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  first kotlin.sequences  firstNotNullOfOrNull kotlin.sequences  flatten kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  groupBy kotlin.sequences  ifEmpty kotlin.sequences  indexOfFirst kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  
mapIndexed kotlin.sequences  
mapNotNull kotlin.sequences  maxOf kotlin.sequences  	maxOrNull kotlin.sequences  minus kotlin.sequences  plus kotlin.sequences  sorted kotlin.sequences  sortedBy kotlin.sequences  
sortedWith kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  
toMutableList kotlin.sequences  toSet kotlin.sequences  	withIndex kotlin.sequences  all kotlin.text  any kotlin.text  	associate kotlin.text  associateBy kotlin.text  
associateWith kotlin.text  contains kotlin.text  count kotlin.text  filter kotlin.text  find kotlin.text  first kotlin.text  firstNotNullOfOrNull kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  groupBy kotlin.text  ifEmpty kotlin.text  indexOfFirst kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapIndexed kotlin.text  
mapNotNull kotlin.text  maxOf kotlin.text  	maxOrNull kotlin.text  plus kotlin.text  replace kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  sumOf kotlin.text  take kotlin.text  toDoubleOrNull kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toLongOrNull kotlin.text  
toMutableList kotlin.text  toSet kotlin.text  trim kotlin.text  	uppercase kotlin.text  	withIndex kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  runBlocking kotlinx.coroutines  COMPLETIONS_COLLECTION !kotlinx.coroutines.CoroutineScope  
Completion !kotlinx.coroutines.CoroutineScope  FirestoreCompletion !kotlinx.coroutines.CoroutineScope  FirestoreConverters !kotlinx.coroutines.CoroutineScope  HabitReorderUiState !kotlinx.coroutines.CoroutineScope  HabitSection !kotlinx.coroutines.CoroutineScope  
HabitSortType !kotlinx.coroutines.CoroutineScope  	HabitType !kotlinx.coroutines.CoroutineScope  Int !kotlinx.coroutines.CoroutineScope  	LocalDate !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  
MainViewModel !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  USERS_COLLECTION !kotlinx.coroutines.CoroutineScope  _completionValuesState !kotlinx.coroutines.CoroutineScope  _completionsState !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  analyticsUseCase !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  	associate !kotlinx.coroutines.CoroutineScope  await !kotlinx.coroutines.CoroutineScope  
awaitClose !kotlinx.coroutines.CoroutineScope  close !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  	compareBy !kotlinx.coroutines.CoroutineScope  completionRepository !kotlinx.coroutines.CoroutineScope  completionsFlow !kotlinx.coroutines.CoroutineScope  
component1 !kotlinx.coroutines.CoroutineScope  
component2 !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  findHabitDocumentId !kotlinx.coroutines.CoroutineScope  firebaseAuth !kotlinx.coroutines.CoroutineScope  	firestore !kotlinx.coroutines.CoroutineScope  firestoreToCompletion !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  flatten !kotlinx.coroutines.CoroutineScope  formatCurrentDate !kotlinx.coroutines.CoroutineScope  formatFrequencyText !kotlinx.coroutines.CoroutineScope  
getWeekNumber !kotlinx.coroutines.CoroutineScope  groupBy !kotlinx.coroutines.CoroutineScope  habitRepository !kotlinx.coroutines.CoroutineScope  habitSectionRepository !kotlinx.coroutines.CoroutineScope  hideCreateDialog !kotlinx.coroutines.CoroutineScope  hideDeleteDialog !kotlinx.coroutines.CoroutineScope  hideEditDialog !kotlinx.coroutines.CoroutineScope  hideMeasurableHabitDialog !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadMeasurableHabitAnalytics !kotlinx.coroutines.CoroutineScope  loadYesNoHabitAnalytics !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  
mapIndexed !kotlinx.coroutines.CoroutineScope  
mapNotNull !kotlinx.coroutines.CoroutineScope  	mapValues !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  showMeasurableHabitDialogNew !kotlinx.coroutines.CoroutineScope  
sortHabits !kotlinx.coroutines.CoroutineScope  
sortedWith !kotlinx.coroutines.CoroutineScope  thenBy !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toDoubleOrNull !kotlinx.coroutines.CoroutineScope  toMap !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  trySend !kotlinx.coroutines.CoroutineScope  userPreferencesRepository !kotlinx.coroutines.CoroutineScope  Main kotlinx.coroutines.Dispatchers  
ChannelResult kotlinx.coroutines.channels  
ProducerScope kotlinx.coroutines.channels  
awaitClose kotlinx.coroutines.channels  COMPLETIONS_COLLECTION )kotlinx.coroutines.channels.ProducerScope  FirestoreCompletion )kotlinx.coroutines.channels.ProducerScope  FirestoreConverters )kotlinx.coroutines.channels.ProducerScope  FirestoreHabit )kotlinx.coroutines.channels.ProducerScope  FirestoreHabitSection )kotlinx.coroutines.channels.ProducerScope  HABITS_COLLECTION )kotlinx.coroutines.channels.ProducerScope  HABIT_SECTIONS_COLLECTION )kotlinx.coroutines.channels.ProducerScope  IllegalStateException )kotlinx.coroutines.channels.ProducerScope  Log )kotlinx.coroutines.channels.ProducerScope  NoSuchElementException )kotlinx.coroutines.channels.ProducerScope  TAG )kotlinx.coroutines.channels.ProducerScope  USERS_COLLECTION )kotlinx.coroutines.channels.ProducerScope  auth )kotlinx.coroutines.channels.ProducerScope  
awaitClose )kotlinx.coroutines.channels.ProducerScope  close )kotlinx.coroutines.channels.ProducerScope  	emptyList )kotlinx.coroutines.channels.ProducerScope  findHabitDocumentId )kotlinx.coroutines.channels.ProducerScope  	firestore )kotlinx.coroutines.channels.ProducerScope  firestoreToCompletion )kotlinx.coroutines.channels.ProducerScope  firestoreToHabit )kotlinx.coroutines.channels.ProducerScope  firestoreToHabitSection )kotlinx.coroutines.channels.ProducerScope  firstNotNullOfOrNull )kotlinx.coroutines.channels.ProducerScope  flatten )kotlinx.coroutines.channels.ProducerScope  java )kotlinx.coroutines.channels.ProducerScope  launch )kotlinx.coroutines.channels.ProducerScope  let )kotlinx.coroutines.channels.ProducerScope  
mapNotNull )kotlinx.coroutines.channels.ProducerScope  
mutableListOf )kotlinx.coroutines.channels.ProducerScope  mutableMapOf )kotlinx.coroutines.channels.ProducerScope  set )kotlinx.coroutines.channels.ProducerScope  trySend )kotlinx.coroutines.channels.ProducerScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  first kotlinx.coroutines.flow  
flatMapLatest kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  map kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  
flatMapLatest kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsState !kotlinx.coroutines.flow.StateFlow  await kotlinx.coroutines.tasks  android android.app.Activity  android android.content.Context  android android.content.ContextWrapper  android  android.view.ContextThemeWrapper  LENGTH_LONG android.widget.Toast  android #androidx.activity.ComponentActivity  android -androidx.activity.ComponentActivity.Companion  android /androidx.compose.animation.AnimatedContentScope  	DateRange +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  Schedule +androidx.compose.foundation.layout.RowScope  	DateRange ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Schedule ,androidx.compose.material.icons.Icons.Filled  Refresh &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  android #androidx.core.app.ComponentActivity  android #androidx.navigation.NavGraphBuilder  android com.example.habits9.ui.details  padding com.example.habits9.ui.details  width com.example.habits9.ui.details  withTimeoutOrNull com.example.habits9.ui.details  error 2com.example.habits9.ui.details.HabitDetailsUiState  	isLoading 2com.example.habits9.ui.details.HabitDetailsUiState  Habit 4com.example.habits9.ui.details.HabitDetailsViewModel  android 4com.example.habits9.ui.details.HabitDetailsViewModel  withTimeoutOrNull 4com.example.habits9.ui.details.HabitDetailsViewModel  android com.example.uhabits_99  android #com.example.uhabits_99.MainActivity  withTimeoutOrNull kotlinx.coroutines  Habit !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  withTimeoutOrNull !kotlinx.coroutines.CoroutineScope  com android.app.Activity  com android.content.Context  com android.content.ContextWrapper  com  android.view.ContextThemeWrapper  com #androidx.activity.ComponentActivity  com -androidx.activity.ComponentActivity.Companion  com /androidx.compose.animation.AnimatedContentScope  	Exception "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  androidx +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  	Exception androidx.compose.material3  Long androidx.compose.material3  	Exception androidx.compose.runtime  Long androidx.compose.runtime  Red "androidx.compose.ui.graphics.Color  Red ,androidx.compose.ui.graphics.Color.Companion  com #androidx.core.app.ComponentActivity  com #androidx.navigation.NavGraphBuilder  deleteAllCompletionsForHabit -com.example.habits9.data.CompletionRepository  let com.example.habits9.data.Habit  deleteHabit (com.example.habits9.data.HabitRepository  updateHabit (com.example.habits9.data.HabitRepository  Long "com.example.habits9.ui.createhabit  find "com.example.habits9.ui.createhabit  fromDatabaseValues "com.example.habits9.ui.createhabit  editingHabitId 5com.example.habits9.ui.createhabit.CreateHabitUiState  
isEditMode 5com.example.habits9.ui.createhabit.CreateHabitUiState  isLoadingHabit 5com.example.habits9.ui.createhabit.CreateHabitUiState  EnhancedFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  find 7com.example.habits9.ui.createhabit.CreateHabitViewModel  fromDatabaseValues 7com.example.habits9.ui.createhabit.CreateHabitViewModel  getHabitForEdit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  initializeForEdit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Habit ;com.example.habits9.ui.createhabit.com.example.habits9.data  	Exception ,com.example.habits9.ui.createmeasurablehabit  Long ,com.example.habits9.ui.createmeasurablehabit  let ,com.example.habits9.ui.createmeasurablehabit  	Exception 'com.example.habits9.ui.createyesnohabit  Long 'com.example.habits9.ui.createyesnohabit  let 'com.example.habits9.ui.createyesnohabit  CompletionRepository com.example.habits9.ui.details  androidx com.example.habits9.ui.details  com com.example.habits9.ui.details  completionRepository com.example.habits9.ui.details  completionRepository 4com.example.habits9.ui.details.HabitDetailsViewModel  deleteHabit 4com.example.habits9.ui.details.HabitDetailsViewModel  example "com.example.habits9.ui.details.com  habits9 *com.example.habits9.ui.details.com.example  data 2com.example.habits9.ui.details.com.example.habits9  	HabitType 7com.example.habits9.ui.details.com.example.habits9.data  com com.example.uhabits_99  com #com.example.uhabits_99.MainActivity  	compareTo kotlin.Long  EnhancedFrequency !kotlinx.coroutines.CoroutineScope  find !kotlinx.coroutines.CoroutineScope  fromDatabaseValues !kotlinx.coroutines.CoroutineScope  findHabitDocumentId (com.example.habits9.data.HabitRepository  habitIdToDocumentIdCache (com.example.habits9.data.HabitRepository  kotlin (com.example.habits9.data.HabitRepository  mutableMapOf (com.example.habits9.data.HabitRepository  set (com.example.habits9.data.HabitRepository  kotlin 2com.example.habits9.data.HabitRepository.Companion  mutableMapOf 2com.example.habits9.data.HabitRepository.Companion  set 2com.example.habits9.data.HabitRepository.Companion  loadedHabitData 5com.example.habits9.ui.createhabit.CreateHabitUiState  remove kotlin.collections.MutableMap  Any "androidx.compose.foundation.layout  CalendarDay "androidx.compose.foundation.layout  ChartDataPoint "androidx.compose.foundation.layout  ComposeBarChart "androidx.compose.foundation.layout  DateTimeFormatter "androidx.compose.foundation.layout  
FontFamily "androidx.compose.foundation.layout  	LocalDate "androidx.compose.foundation.layout  Map "androidx.compose.foundation.layout  MonthCalendarGrid "androidx.compose.foundation.layout  	MonthData "androidx.compose.foundation.layout  Size "androidx.compose.foundation.layout  	TextStyle "androidx.compose.foundation.layout  
TimePeriod "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  filterIsInstance "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  generateMonthsToShow "androidx.compose.foundation.layout  get "androidx.compose.foundation.layout  maxOfOrNull "androidx.compose.foundation.layout  	maxOrNull "androidx.compose.foundation.layout  
menuAnchor "androidx.compose.foundation.layout  
mutableListOf "androidx.compose.foundation.layout  
FontFamily +androidx.compose.foundation.layout.BoxScope  AnalyticsChartsSection .androidx.compose.foundation.layout.ColumnScope  CalendarDay .androidx.compose.foundation.layout.ColumnScope  CompletionCalendarHeatmap .androidx.compose.foundation.layout.ColumnScope  CompletionHistoryChart .androidx.compose.foundation.layout.ColumnScope  ComposeBarChart .androidx.compose.foundation.layout.ColumnScope  	LocalDate .androidx.compose.foundation.layout.ColumnScope  MonthCalendarGrid .androidx.compose.foundation.layout.ColumnScope  
TimePeriod .androidx.compose.foundation.layout.ColumnScope  filterIsInstance .androidx.compose.foundation.layout.ColumnScope  generateMonthsToShow .androidx.compose.foundation.layout.ColumnScope  get .androidx.compose.foundation.layout.ColumnScope  	lowercase .androidx.compose.foundation.layout.ColumnScope  	mapValues .androidx.compose.foundation.layout.ColumnScope  	maxOrNull .androidx.compose.foundation.layout.ColumnScope  
menuAnchor .androidx.compose.foundation.layout.ColumnScope  replaceFirstChar .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  CalendarDay +androidx.compose.foundation.layout.RowScope  ExposedDropdownMenuBox +androidx.compose.foundation.layout.RowScope  ExposedDropdownMenuDefaults +androidx.compose.foundation.layout.RowScope  
TimePeriod +androidx.compose.foundation.layout.RowScope  TrailingIcon +androidx.compose.foundation.layout.RowScope  get +androidx.compose.foundation.layout.RowScope  	lowercase +androidx.compose.foundation.layout.RowScope  
menuAnchor +androidx.compose.foundation.layout.RowScope  replaceFirstChar +androidx.compose.foundation.layout.RowScope  	uppercase +androidx.compose.foundation.layout.RowScope  MonthCalendarGrid .androidx.compose.foundation.lazy.LazyItemScope  MonthCalendarGrid .androidx.compose.foundation.lazy.LazyListScope  Any androidx.compose.material3  CalendarDay androidx.compose.material3  
CardElevation androidx.compose.material3  ChartDataPoint androidx.compose.material3  ComposeBarChart androidx.compose.material3  DateTimeFormatter androidx.compose.material3  
FontFamily androidx.compose.material3  	LocalDate androidx.compose.material3  Map androidx.compose.material3  MonthCalendarGrid androidx.compose.material3  	MonthData androidx.compose.material3  Size androidx.compose.material3  	TextStyle androidx.compose.material3  
TimePeriod androidx.compose.material3  
cardElevation androidx.compose.material3  filterIsInstance androidx.compose.material3  forEachIndexed androidx.compose.material3  generateMonthsToShow androidx.compose.material3  get androidx.compose.material3  maxOfOrNull androidx.compose.material3  	maxOrNull androidx.compose.material3  
menuAnchor androidx.compose.material3  
mutableListOf androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  
FontFamily 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
TimePeriod 6androidx.compose.material3.ExposedDropdownMenuBoxScope  androidx 6androidx.compose.material3.ExposedDropdownMenuBoxScope  dp 6androidx.compose.material3.ExposedDropdownMenuBoxScope  forEach 6androidx.compose.material3.ExposedDropdownMenuBoxScope  height 6androidx.compose.material3.ExposedDropdownMenuBoxScope  	lowercase 6androidx.compose.material3.ExposedDropdownMenuBoxScope  replaceFirstChar 6androidx.compose.material3.ExposedDropdownMenuBoxScope  sp 6androidx.compose.material3.ExposedDropdownMenuBoxScope  	uppercase 6androidx.compose.material3.ExposedDropdownMenuBoxScope  width 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Any androidx.compose.runtime  CalendarDay androidx.compose.runtime  ChartDataPoint androidx.compose.runtime  ComposeBarChart androidx.compose.runtime  DateTimeFormatter androidx.compose.runtime  
FontFamily androidx.compose.runtime  	LocalDate androidx.compose.runtime  Map androidx.compose.runtime  MonthCalendarGrid androidx.compose.runtime  	MonthData androidx.compose.runtime  Size androidx.compose.runtime  	TextStyle androidx.compose.runtime  
TimePeriod androidx.compose.runtime  
cardElevation androidx.compose.runtime  filterIsInstance androidx.compose.runtime  forEachIndexed androidx.compose.runtime  generateMonthsToShow androidx.compose.runtime  get androidx.compose.runtime  maxOfOrNull androidx.compose.runtime  	maxOrNull androidx.compose.runtime  
menuAnchor androidx.compose.runtime  
mutableListOf androidx.compose.runtime  border androidx.compose.ui.Modifier  
menuAnchor &androidx.compose.ui.Modifier.Companion  nativeCanvas androidx.compose.ui.graphics  
FontFamily 0androidx.compose.ui.graphics.drawscope.DrawScope  Size 0androidx.compose.ui.graphics.drawscope.DrawScope  String 0androidx.compose.ui.graphics.drawscope.DrawScope  	TextStyle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  drawText 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed 0androidx.compose.ui.graphics.drawscope.DrawScope  format 0androidx.compose.ui.graphics.drawscope.DrawScope  maxOfOrNull 0androidx.compose.ui.graphics.drawscope.DrawScope  sp 0androidx.compose.ui.graphics.drawscope.DrawScope  TextLayoutResult androidx.compose.ui.text  TextMeasurer androidx.compose.ui.text  drawText androidx.compose.ui.text  rememberTextMeasurer androidx.compose.ui.text  size )androidx.compose.ui.text.TextLayoutResult  measure %androidx.compose.ui.text.TextMeasurer  IntSize androidx.compose.ui.unit  width  androidx.compose.ui.unit.IntSize  name -com.example.habits9.data.analytics.TimePeriod  values -com.example.habits9.data.analytics.TimePeriod  AnalyticsChartsSection com.example.habits9.ui.details  Any com.example.habits9.ui.details  CompletionCalendarHeatmap com.example.habits9.ui.details  CompletionHistoryChart com.example.habits9.ui.details  currentTimePeriod 2com.example.habits9.ui.details.HabitDetailsUiState  updateChartTimePeriod 4com.example.habits9.ui.details.HabitDetailsViewModel  	Alignment )com.example.habits9.ui.details.components  Any )com.example.habits9.ui.details.components  Arrangement )com.example.habits9.ui.details.components  Boolean )com.example.habits9.ui.details.components  Box )com.example.habits9.ui.details.components  CalendarDay )com.example.habits9.ui.details.components  Card )com.example.habits9.ui.details.components  CardDefaults )com.example.habits9.ui.details.components  ChartDataPoint )com.example.habits9.ui.details.components  Color )com.example.habits9.ui.details.components  Column )com.example.habits9.ui.details.components  CompletionCalendarHeatmap )com.example.habits9.ui.details.components  CompletionHistoryChart )com.example.habits9.ui.details.components  
Composable )com.example.habits9.ui.details.components  ComposeBarChart )com.example.habits9.ui.details.components  DateTimeFormatter )com.example.habits9.ui.details.components  DropdownMenuItem )com.example.habits9.ui.details.components  ExposedDropdownMenuBox )com.example.habits9.ui.details.components  ExposedDropdownMenuDefaults )com.example.habits9.ui.details.components  Float )com.example.habits9.ui.details.components  
FontFamily )com.example.habits9.ui.details.components  
FontWeight )com.example.habits9.ui.details.components  LazyRow )com.example.habits9.ui.details.components  List )com.example.habits9.ui.details.components  	LocalDate )com.example.habits9.ui.details.components  Map )com.example.habits9.ui.details.components  
MaterialTheme )com.example.habits9.ui.details.components  Modifier )com.example.habits9.ui.details.components  MonthCalendarGrid )com.example.habits9.ui.details.components  	MonthData )com.example.habits9.ui.details.components  Offset )com.example.habits9.ui.details.components  OutlinedTextField )com.example.habits9.ui.details.components  OutlinedTextFieldDefaults )com.example.habits9.ui.details.components  Row )com.example.habits9.ui.details.components  Size )com.example.habits9.ui.details.components  Spacer )com.example.habits9.ui.details.components  String )com.example.habits9.ui.details.components  Text )com.example.habits9.ui.details.components  	TextAlign )com.example.habits9.ui.details.components  	TextStyle )com.example.habits9.ui.details.components  
TimePeriod )com.example.habits9.ui.details.components  TrailingIcon )com.example.habits9.ui.details.components  Unit )com.example.habits9.ui.details.components  androidx )com.example.habits9.ui.details.components  
cardColors )com.example.habits9.ui.details.components  
cardElevation )com.example.habits9.ui.details.components  colors )com.example.habits9.ui.details.components  fillMaxWidth )com.example.habits9.ui.details.components  filterIsInstance )com.example.habits9.ui.details.components  forEach )com.example.habits9.ui.details.components  forEachIndexed )com.example.habits9.ui.details.components  format )com.example.habits9.ui.details.components  generateMonthsToShow )com.example.habits9.ui.details.components  generateWeeksForMonth )com.example.habits9.ui.details.components  get )com.example.habits9.ui.details.components  getValue )com.example.habits9.ui.details.components  height )com.example.habits9.ui.details.components  
isNotEmpty )com.example.habits9.ui.details.components  listOf )com.example.habits9.ui.details.components  	lowercase )com.example.habits9.ui.details.components  maxOfOrNull )com.example.habits9.ui.details.components  	maxOrNull )com.example.habits9.ui.details.components  
menuAnchor )com.example.habits9.ui.details.components  
mutableListOf )com.example.habits9.ui.details.components  mutableStateOf )com.example.habits9.ui.details.components  padding )com.example.habits9.ui.details.components  provideDelegate )com.example.habits9.ui.details.components  remember )com.example.habits9.ui.details.components  replaceFirstChar )com.example.habits9.ui.details.components  setValue )com.example.habits9.ui.details.components  size )com.example.habits9.ui.details.components  spacedBy )com.example.habits9.ui.details.components  	uppercase )com.example.habits9.ui.details.components  width )com.example.habits9.ui.details.components  	monthName 3com.example.habits9.ui.details.components.MonthData  weeks 3com.example.habits9.ui.details.components.MonthData  	compareTo java.time.LocalDate  dp 
kotlin.Double  
unaryMinus 
kotlin.Double  rem kotlin.Float  filterIsInstance kotlin.collections  get kotlin.collections  maxOfOrNull kotlin.collections  filterIsInstance kotlin.collections.Collection  maxOfOrNull kotlin.collections.List  
isNotEmpty kotlin.collections.Map  max kotlin.math  min kotlin.math  filterIsInstance kotlin.sequences  maxOfOrNull kotlin.sequences  
MatchGroup kotlin.text  get kotlin.text  maxOfOrNull kotlin.text  
MetricView +androidx.compose.foundation.layout.BoxScope  Box com.example.habits9.ui.details  Canvas "androidx.compose.foundation.layout  	DrawScope "androidx.compose.foundation.layout  Paint "androidx.compose.foundation.layout  Path "androidx.compose.foundation.layout  
PathEffect "androidx.compose.foundation.layout  
ScorePoint "androidx.compose.foundation.layout  Stroke "androidx.compose.foundation.layout  	StrokeCap "androidx.compose.foundation.layout  
StrokeJoin "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  dashPathEffect "androidx.compose.foundation.layout  first "androidx.compose.foundation.layout  floatArrayOf "androidx.compose.foundation.layout  last "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  sortedBy "androidx.compose.foundation.layout  until "androidx.compose.foundation.layout  Canvas .androidx.compose.foundation.layout.ColumnScope  
ScoreChart .androidx.compose.foundation.layout.ColumnScope  
ScorePoint .androidx.compose.foundation.layout.ColumnScope  drawScoreChart .androidx.compose.foundation.layout.ColumnScope  map .androidx.compose.foundation.layout.ColumnScope  Canvas androidx.compose.material3  	DrawScope androidx.compose.material3  Paint androidx.compose.material3  Path androidx.compose.material3  
PathEffect androidx.compose.material3  
ScorePoint androidx.compose.material3  Stroke androidx.compose.material3  	StrokeCap androidx.compose.material3  
StrokeJoin androidx.compose.material3  apply androidx.compose.material3  dashPathEffect androidx.compose.material3  first androidx.compose.material3  floatArrayOf androidx.compose.material3  last androidx.compose.material3  map androidx.compose.material3  sortedBy androidx.compose.material3  until androidx.compose.material3  Canvas androidx.compose.runtime  	DrawScope androidx.compose.runtime  Paint androidx.compose.runtime  Path androidx.compose.runtime  
PathEffect androidx.compose.runtime  
ScorePoint androidx.compose.runtime  Stroke androidx.compose.runtime  	StrokeCap androidx.compose.runtime  
StrokeJoin androidx.compose.runtime  apply androidx.compose.runtime  dashPathEffect androidx.compose.runtime  first androidx.compose.runtime  floatArrayOf androidx.compose.runtime  last androidx.compose.runtime  map androidx.compose.runtime  sortedBy androidx.compose.runtime  until androidx.compose.runtime  	Alignment androidx.compose.ui.graphics  Arrangement androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  Card androidx.compose.ui.graphics  CardDefaults androidx.compose.ui.graphics  Column androidx.compose.ui.graphics  
Composable androidx.compose.ui.graphics  Double androidx.compose.ui.graphics  	DrawScope androidx.compose.ui.graphics  DropdownMenuItem androidx.compose.ui.graphics  ExposedDropdownMenuBox androidx.compose.ui.graphics  ExposedDropdownMenuDefaults androidx.compose.ui.graphics  Float androidx.compose.ui.graphics  
FontFamily androidx.compose.ui.graphics  
FontWeight androidx.compose.ui.graphics  List androidx.compose.ui.graphics  Long androidx.compose.ui.graphics  
MaterialTheme androidx.compose.ui.graphics  Modifier androidx.compose.ui.graphics  Offset androidx.compose.ui.graphics  OutlinedTextField androidx.compose.ui.graphics  OutlinedTextFieldDefaults androidx.compose.ui.graphics  Paint androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  
PathEffect androidx.compose.ui.graphics  Row androidx.compose.ui.graphics  
ScorePoint androidx.compose.ui.graphics  Spacer androidx.compose.ui.graphics  Stroke androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  
StrokeJoin androidx.compose.ui.graphics  Text androidx.compose.ui.graphics  
TimePeriod androidx.compose.ui.graphics  TrailingIcon androidx.compose.ui.graphics  Unit androidx.compose.ui.graphics  androidx androidx.compose.ui.graphics  apply androidx.compose.ui.graphics  
cardColors androidx.compose.ui.graphics  
cardElevation androidx.compose.ui.graphics  colors androidx.compose.ui.graphics  dashPathEffect androidx.compose.ui.graphics  fillMaxWidth androidx.compose.ui.graphics  first androidx.compose.ui.graphics  floatArrayOf androidx.compose.ui.graphics  forEach androidx.compose.ui.graphics  getValue androidx.compose.ui.graphics  height androidx.compose.ui.graphics  last androidx.compose.ui.graphics  	lowercase androidx.compose.ui.graphics  map androidx.compose.ui.graphics  
menuAnchor androidx.compose.ui.graphics  mutableStateOf androidx.compose.ui.graphics  padding androidx.compose.ui.graphics  provideDelegate androidx.compose.ui.graphics  remember androidx.compose.ui.graphics  replaceFirstChar androidx.compose.ui.graphics  setValue androidx.compose.ui.graphics  sortedBy androidx.compose.ui.graphics  until androidx.compose.ui.graphics  	uppercase androidx.compose.ui.graphics  width androidx.compose.ui.graphics  
PathEffect "androidx.compose.ui.graphics.Paint  apply "androidx.compose.ui.graphics.Paint  color "androidx.compose.ui.graphics.Paint  dashPathEffect "androidx.compose.ui.graphics.Paint  floatArrayOf "androidx.compose.ui.graphics.Paint  
pathEffect "androidx.compose.ui.graphics.Paint  strokeWidth "androidx.compose.ui.graphics.Paint  lineTo !androidx.compose.ui.graphics.Path  moveTo !androidx.compose.ui.graphics.Path  	Companion 'androidx.compose.ui.graphics.PathEffect  dashPathEffect 'androidx.compose.ui.graphics.PathEffect  dashPathEffect 1androidx.compose.ui.graphics.PathEffect.Companion  	Companion &androidx.compose.ui.graphics.StrokeCap  Round &androidx.compose.ui.graphics.StrokeCap  Round 0androidx.compose.ui.graphics.StrokeCap.Companion  	Companion 'androidx.compose.ui.graphics.StrokeJoin  Round 'androidx.compose.ui.graphics.StrokeJoin  Round 1androidx.compose.ui.graphics.StrokeJoin.Companion  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Paint 0androidx.compose.ui.graphics.drawscope.DrawScope  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  
PathEffect 0androidx.compose.ui.graphics.drawscope.DrawScope  	StrokeCap 0androidx.compose.ui.graphics.drawscope.DrawScope  
StrokeJoin 0androidx.compose.ui.graphics.drawscope.DrawScope  apply 0androidx.compose.ui.graphics.drawscope.DrawScope  dashPathEffect 0androidx.compose.ui.graphics.drawscope.DrawScope  drawGrid 0androidx.compose.ui.graphics.drawscope.DrawScope  drawPath 0androidx.compose.ui.graphics.drawscope.DrawScope  drawScoreChart 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawScoreLine 0androidx.compose.ui.graphics.drawscope.DrawScope  drawScorePoints 0androidx.compose.ui.graphics.drawscope.DrawScope  first 0androidx.compose.ui.graphics.drawscope.DrawScope  floatArrayOf 0androidx.compose.ui.graphics.drawscope.DrawScope  last 0androidx.compose.ui.graphics.drawscope.DrawScope  map 0androidx.compose.ui.graphics.drawscope.DrawScope  sortedBy 0androidx.compose.ui.graphics.drawscope.DrawScope  until 0androidx.compose.ui.graphics.drawscope.DrawScope  LocalDensity androidx.compose.ui.platform  ScoreDataPoint "com.example.habits9.data.analytics  ScoreDataPoint 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  calculatePeriodScore 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  getScoreHistory 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  scoreHistory ;com.example.habits9.data.analytics.MeasurableHabitAnalytics  score 1com.example.habits9.data.analytics.ScoreDataPoint  	timestamp 1com.example.habits9.data.analytics.ScoreDataPoint  scoreHistory 6com.example.habits9.data.analytics.YesNoHabitAnalytics  
ScoreChart com.example.habits9.ui.details  
ScorePoint com.example.habits9.ui.details  Canvas )com.example.habits9.ui.details.components  Double )com.example.habits9.ui.details.components  	DrawScope )com.example.habits9.ui.details.components  Long )com.example.habits9.ui.details.components  Paint )com.example.habits9.ui.details.components  Path )com.example.habits9.ui.details.components  
PathEffect )com.example.habits9.ui.details.components  
ScoreChart )com.example.habits9.ui.details.components  
ScorePoint )com.example.habits9.ui.details.components  Stroke )com.example.habits9.ui.details.components  	StrokeCap )com.example.habits9.ui.details.components  
StrokeJoin )com.example.habits9.ui.details.components  apply )com.example.habits9.ui.details.components  dashPathEffect )com.example.habits9.ui.details.components  drawGrid )com.example.habits9.ui.details.components  drawScoreChart )com.example.habits9.ui.details.components  
drawScoreLine )com.example.habits9.ui.details.components  drawScorePoints )com.example.habits9.ui.details.components  first )com.example.habits9.ui.details.components  floatArrayOf )com.example.habits9.ui.details.components  last )com.example.habits9.ui.details.components  map )com.example.habits9.ui.details.components  sortedBy )com.example.habits9.ui.details.components  until )com.example.habits9.ui.details.components  	timestamp 4com.example.habits9.ui.details.components.ScorePoint  value 4com.example.habits9.ui.details.components.ScorePoint  apply kotlin  floatArrayOf kotlin  minus kotlin.Long  toFloat kotlin.Long  last kotlin.collections  last kotlin.collections.List  last 
kotlin.ranges  last kotlin.sequences  last kotlin.text  Date "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  SimpleDateFormat "androidx.compose.foundation.layout  TextMeasurer "androidx.compose.foundation.layout  indices "androidx.compose.foundation.layout  
mapIndexed "androidx.compose.foundation.layout  rememberTextMeasurer "androidx.compose.foundation.layout  rememberTextMeasurer .androidx.compose.foundation.layout.ColumnScope  Date androidx.compose.material3  Locale androidx.compose.material3  SimpleDateFormat androidx.compose.material3  TextMeasurer androidx.compose.material3  indices androidx.compose.material3  
mapIndexed androidx.compose.material3  rememberTextMeasurer androidx.compose.material3  Date androidx.compose.runtime  Locale androidx.compose.runtime  SimpleDateFormat androidx.compose.runtime  TextMeasurer androidx.compose.runtime  indices androidx.compose.runtime  
mapIndexed androidx.compose.runtime  rememberTextMeasurer androidx.compose.runtime  Date androidx.compose.ui.graphics  Locale androidx.compose.ui.graphics  SimpleDateFormat androidx.compose.ui.graphics  TextMeasurer androidx.compose.ui.graphics  	TextStyle androidx.compose.ui.graphics  forEachIndexed androidx.compose.ui.graphics  indices androidx.compose.ui.graphics  listOf androidx.compose.ui.graphics  
mapIndexed androidx.compose.ui.graphics  rememberTextMeasurer androidx.compose.ui.graphics  toList androidx.compose.ui.graphics  Date 0androidx.compose.ui.graphics.drawscope.DrawScope  
FontWeight 0androidx.compose.ui.graphics.drawscope.DrawScope  Locale 0androidx.compose.ui.graphics.drawscope.DrawScope  SimpleDateFormat 0androidx.compose.ui.graphics.drawscope.DrawScope  drawDataPointLabels 0androidx.compose.ui.graphics.drawscope.DrawScope  drawGridWithLabels 0androidx.compose.ui.graphics.drawscope.DrawScope  drawXAxisLabels 0androidx.compose.ui.graphics.drawscope.DrawScope  indices 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  
mapIndexed 0androidx.compose.ui.graphics.drawscope.DrawScope  toList 0androidx.compose.ui.graphics.drawscope.DrawScope  height  androidx.compose.ui.unit.IntSize  Date )com.example.habits9.ui.details.components  Locale )com.example.habits9.ui.details.components  SimpleDateFormat )com.example.habits9.ui.details.components  TextMeasurer )com.example.habits9.ui.details.components  drawDataPointLabels )com.example.habits9.ui.details.components  drawGridWithLabels )com.example.habits9.ui.details.components  drawXAxisLabels )com.example.habits9.ui.details.components  indices )com.example.habits9.ui.details.components  
mapIndexed )com.example.habits9.ui.details.components  rememberTextMeasurer )com.example.habits9.ui.details.components  toList )com.example.habits9.ui.details.components  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	Alignment 	java.util  Arrangement 	java.util  Canvas 	java.util  Card 	java.util  CardDefaults 	java.util  Color 	java.util  Column 	java.util  
Composable 	java.util  Date 	java.util  Double 	java.util  	DrawScope 	java.util  DropdownMenuItem 	java.util  ExposedDropdownMenuBox 	java.util  ExposedDropdownMenuDefaults 	java.util  Float 	java.util  
FontFamily 	java.util  
FontWeight 	java.util  List 	java.util  Long 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  Offset 	java.util  OutlinedTextField 	java.util  OutlinedTextFieldDefaults 	java.util  Path 	java.util  
PathEffect 	java.util  Row 	java.util  
ScorePoint 	java.util  SimpleDateFormat 	java.util  Spacer 	java.util  Stroke 	java.util  	StrokeCap 	java.util  
StrokeJoin 	java.util  Text 	java.util  TextMeasurer 	java.util  	TextStyle 	java.util  
TimePeriod 	java.util  TrailingIcon 	java.util  Unit 	java.util  androidx 	java.util  
cardColors 	java.util  
cardElevation 	java.util  colors 	java.util  dashPathEffect 	java.util  fillMaxWidth 	java.util  first 	java.util  floatArrayOf 	java.util  forEach 	java.util  forEachIndexed 	java.util  getValue 	java.util  height 	java.util  indices 	java.util  last 	java.util  listOf 	java.util  	lowercase 	java.util  
mapIndexed 	java.util  
menuAnchor 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  rememberTextMeasurer 	java.util  replaceFirstChar 	java.util  setValue 	java.util  sortedBy 	java.util  toList 	java.util  until 	java.util  	uppercase 	java.util  width 	java.util  times 
kotlin.Double  indices kotlin.collections  indices kotlin.collections.List  toList kotlin.ranges.IntRange  indices kotlin.text  Instant "androidx.compose.foundation.layout  
WeekFields "androidx.compose.foundation.layout  ZoneId "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  formatTimePeriodLabel "androidx.compose.foundation.layout  horizontalScroll "androidx.compose.foundation.layout  maxOf "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  takeLast "androidx.compose.foundation.layout  Canvas +androidx.compose.foundation.layout.BoxScope  drawScoreChart +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight .androidx.compose.foundation.layout.ColumnScope  maxOf .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  Instant androidx.compose.material3  
WeekFields androidx.compose.material3  ZoneId androidx.compose.material3  
fillMaxHeight androidx.compose.material3  formatTimePeriodLabel androidx.compose.material3  horizontalScroll androidx.compose.material3  maxOf androidx.compose.material3  rememberScrollState androidx.compose.material3  takeLast androidx.compose.material3  Instant androidx.compose.runtime  
WeekFields androidx.compose.runtime  ZoneId androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  formatTimePeriodLabel androidx.compose.runtime  horizontalScroll androidx.compose.runtime  maxOf androidx.compose.runtime  rememberScrollState androidx.compose.runtime  takeLast androidx.compose.runtime  
fillMaxHeight androidx.compose.ui.Modifier  Box androidx.compose.ui.graphics  DateTimeFormatter androidx.compose.ui.graphics  Instant androidx.compose.ui.graphics  String androidx.compose.ui.graphics  
WeekFields androidx.compose.ui.graphics  ZoneId androidx.compose.ui.graphics  
fillMaxHeight androidx.compose.ui.graphics  formatTimePeriodLabel androidx.compose.ui.graphics  horizontalScroll androidx.compose.ui.graphics  maxOf androidx.compose.ui.graphics  rememberScrollState androidx.compose.ui.graphics  takeLast androidx.compose.ui.graphics  formatTimePeriodLabel 0androidx.compose.ui.graphics.drawscope.DrawScope  Instant )com.example.habits9.ui.details.components  
WeekFields )com.example.habits9.ui.details.components  ZoneId )com.example.habits9.ui.details.components  
fillMaxHeight )com.example.habits9.ui.details.components  formatTimePeriodLabel )com.example.habits9.ui.details.components  horizontalScroll )com.example.habits9.ui.details.components  maxOf )com.example.habits9.ui.details.components  rememberScrollState )com.example.habits9.ui.details.components  takeLast )com.example.habits9.ui.details.components  Box 	java.util  DateTimeFormatter 	java.util  Instant 	java.util  String 	java.util  
WeekFields 	java.util  ZoneId 	java.util  
fillMaxHeight 	java.util  formatTimePeriodLabel 	java.util  horizontalScroll 	java.util  maxOf 	java.util  rememberScrollState 	java.util  takeLast 	java.util  dp 
kotlin.Int  takeLast 
kotlin.String  takeLast kotlin.collections  takeLast kotlin.text  value 'androidx.compose.foundation.ScrollState  formatCompactTimePeriodLabel "androidx.compose.foundation.layout  measureMaxTextWidth "androidx.compose.foundation.layout  minOf "androidx.compose.foundation.layout  drawCompactScoreChart .androidx.compose.foundation.layout.ColumnScope  ui 3androidx.compose.foundation.layout.androidx.compose  ScrollState >androidx.compose.foundation.layout.androidx.compose.foundation  geometry 6androidx.compose.foundation.layout.androidx.compose.ui  Rect ?androidx.compose.foundation.layout.androidx.compose.ui.geometry  formatCompactTimePeriodLabel androidx.compose.material3  measureMaxTextWidth androidx.compose.material3  minOf androidx.compose.material3  ui +androidx.compose.material3.androidx.compose  ScrollState 6androidx.compose.material3.androidx.compose.foundation  geometry .androidx.compose.material3.androidx.compose.ui  Rect 7androidx.compose.material3.androidx.compose.ui.geometry  formatCompactTimePeriodLabel androidx.compose.runtime  measureMaxTextWidth androidx.compose.runtime  minOf androidx.compose.runtime  ui )androidx.compose.runtime.androidx.compose  ScrollState 4androidx.compose.runtime.androidx.compose.foundation  geometry ,androidx.compose.runtime.androidx.compose.ui  Rect 5androidx.compose.runtime.androidx.compose.ui.geometry  Rect androidx.compose.ui.geometry  let #androidx.compose.ui.geometry.Offset  bottom !androidx.compose.ui.geometry.Rect  height !androidx.compose.ui.geometry.Rect  left !androidx.compose.ui.geometry.Rect  right !androidx.compose.ui.geometry.Rect  top !androidx.compose.ui.geometry.Rect  Int androidx.compose.ui.graphics  formatCompactTimePeriodLabel androidx.compose.ui.graphics  let androidx.compose.ui.graphics  measureMaxTextWidth androidx.compose.ui.graphics  minOf androidx.compose.ui.graphics  compose %androidx.compose.ui.graphics.androidx  
foundation -androidx.compose.ui.graphics.androidx.compose  ui -androidx.compose.ui.graphics.androidx.compose  ScrollState 8androidx.compose.ui.graphics.androidx.compose.foundation  geometry 0androidx.compose.ui.graphics.androidx.compose.ui  Rect 9androidx.compose.ui.graphics.androidx.compose.ui.geometry  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  drawCompactFooter 0androidx.compose.ui.graphics.drawscope.DrawScope  drawCompactGrid 0androidx.compose.ui.graphics.drawscope.DrawScope  drawCompactMarker 0androidx.compose.ui.graphics.drawscope.DrawScope  drawCompactScoreChart 0androidx.compose.ui.graphics.drawscope.DrawScope  formatCompactTimePeriodLabel 0androidx.compose.ui.graphics.drawscope.DrawScope  let 0androidx.compose.ui.graphics.drawscope.DrawScope  maxOf 0androidx.compose.ui.graphics.drawscope.DrawScope  measureMaxTextWidth 0androidx.compose.ui.graphics.drawscope.DrawScope  minOf 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx !androidx.compose.ui.unit.TextUnit  Int )com.example.habits9.ui.details.components  drawCompactFooter )com.example.habits9.ui.details.components  drawCompactGrid )com.example.habits9.ui.details.components  drawCompactMarker )com.example.habits9.ui.details.components  drawCompactScoreChart )com.example.habits9.ui.details.components  formatCompactTimePeriodLabel )com.example.habits9.ui.details.components  let )com.example.habits9.ui.details.components  measureMaxTextWidth )com.example.habits9.ui.details.components  minOf )com.example.habits9.ui.details.components  compose 2com.example.habits9.ui.details.components.androidx  
foundation :com.example.habits9.ui.details.components.androidx.compose  ui :com.example.habits9.ui.details.components.androidx.compose  ScrollState Ecom.example.habits9.ui.details.components.androidx.compose.foundation  geometry =com.example.habits9.ui.details.components.androidx.compose.ui  Rect Fcom.example.habits9.ui.details.components.androidx.compose.ui.geometry  Int 	java.util  formatCompactTimePeriodLabel 	java.util  let 	java.util  measureMaxTextWidth 	java.util  minOf 	java.util  compose java.util.androidx  
foundation java.util.androidx.compose  ui java.util.androidx.compose  ScrollState %java.util.androidx.compose.foundation  geometry java.util.androidx.compose.ui  Rect &java.util.androidx.compose.ui.geometry  sp kotlin.Float  minOf kotlin.collections  maxOf kotlin.collections.List  minOf kotlin.comparisons  maxOf kotlin.ranges.IntRange  minOf kotlin.sequences  minOf kotlin.text  	GridCells "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  PerformanceBlock "androidx.compose.foundation.layout  PerformanceDataPoint "androidx.compose.foundation.layout  	GridCells .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  PerformanceBlock .androidx.compose.foundation.layout.ColumnScope  PerformanceDataPoint .androidx.compose.foundation.layout.ColumnScope  PerformanceHeatmap .androidx.compose.foundation.layout.ColumnScope  formatTimePeriodLabel .androidx.compose.foundation.layout.ColumnScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Adaptive /androidx.compose.foundation.lazy.grid.GridCells  PerformanceBlock 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  PerformanceBlock 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  	GridCells androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  PerformanceBlock androidx.compose.material3  PerformanceDataPoint androidx.compose.material3  
MaterialTheme 6androidx.compose.material3.ExposedDropdownMenuBoxScope  	GridCells androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  PerformanceBlock androidx.compose.runtime  PerformanceDataPoint androidx.compose.runtime  	luminance androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	luminance "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  DAY -com.example.habits9.data.analytics.TimePeriod  PerformanceDataPoint com.example.habits9.ui.details  PerformanceHeatmap com.example.habits9.ui.details  	GridCells )com.example.habits9.ui.details.components  LazyVerticalGrid )com.example.habits9.ui.details.components  
PaddingValues )com.example.habits9.ui.details.components  PerformanceBlock )com.example.habits9.ui.details.components  PerformanceDataPoint )com.example.habits9.ui.details.components  PerformanceHeatmap )com.example.habits9.ui.details.components  fillMaxSize )com.example.habits9.ui.details.components  getContrastTextColor )com.example.habits9.ui.details.components  getPerformanceColor )com.example.habits9.ui.details.components  score >com.example.habits9.ui.details.components.PerformanceDataPoint  	timestamp >com.example.habits9.ui.details.components.PerformanceDataPoint  	GridCells 	java.util  LazyVerticalGrid 	java.util  
PaddingValues 	java.util  PerformanceBlock 	java.util  PerformanceDataPoint 	java.util  	TextAlign 	java.util  fillMaxSize 	java.util  
isNotEmpty 	java.util  size 	java.util  spacedBy 	java.util  HabitScoringEngine "com.example.habits9.data.analytics  
scoringEngine 8com.example.habits9.data.analytics.HabitAnalyticsUseCase  
Completion  com.example.habits9.data.scoring  Double  com.example.habits9.data.scoring  Habit  com.example.habits9.data.scoring  HabitScheduler  com.example.habits9.data.scoring  HabitScoringEngine  com.example.habits9.data.scoring  	HabitType  com.example.habits9.data.scoring  Inject  com.example.habits9.data.scoring  List  com.example.habits9.data.scoring  	LocalDate  com.example.habits9.data.scoring  Long  com.example.habits9.data.scoring  Map  com.example.habits9.data.scoring  NumericalHabitType  com.example.habits9.data.scoring  Pair  com.example.habits9.data.scoring  	Singleton  com.example.habits9.data.scoring  
TimePeriod  com.example.habits9.data.scoring  ZoneId  com.example.habits9.data.scoring  associateBy  com.example.habits9.data.scoring  isHabitScheduled  com.example.habits9.data.scoring  
isNullOrEmpty  com.example.habits9.data.scoring  kotlin  com.example.habits9.data.scoring  
plusAssign  com.example.habits9.data.scoring  toDoubleOrNull  com.example.habits9.data.scoring  HabitScheduler 3com.example.habits9.data.scoring.HabitScoringEngine  	HabitType 3com.example.habits9.data.scoring.HabitScoringEngine  	LocalDate 3com.example.habits9.data.scoring.HabitScoringEngine  NumericalHabitType 3com.example.habits9.data.scoring.HabitScoringEngine  Pair 3com.example.habits9.data.scoring.HabitScoringEngine  
TimePeriod 3com.example.habits9.data.scoring.HabitScoringEngine  ZoneId 3com.example.habits9.data.scoring.HabitScoringEngine  associateBy 3com.example.habits9.data.scoring.HabitScoringEngine  calculateDailyScore 3com.example.habits9.data.scoring.HabitScoringEngine  getScoreForPeriod 3com.example.habits9.data.scoring.HabitScoringEngine  isHabitScheduled 3com.example.habits9.data.scoring.HabitScoringEngine  
isNullOrEmpty 3com.example.habits9.data.scoring.HabitScoringEngine  kotlin 3com.example.habits9.data.scoring.HabitScoringEngine  
plusAssign 3com.example.habits9.data.scoring.HabitScoringEngine  toDoubleOrNull 3com.example.habits9.data.scoring.HabitScoringEngine  HabitScoringEngine com.example.habits9.ui  HabitScoringEngine $com.example.habits9.ui.MainViewModel  
scoringEngine $com.example.habits9.ui.MainViewModel  
withDayOfYear java.time.LocalDate  	withMonth java.time.LocalDate  plus 
kotlin.Double  
plusAssign 
kotlin.Double  Int  com.example.habits9.data.scoring  Math  com.example.habits9.data.scoring  ScoringVerification  com.example.habits9.data.scoring  String  com.example.habits9.data.scoring  
StringBuilder  com.example.habits9.data.scoring  
appendLine  com.example.habits9.data.scoring  downTo  com.example.habits9.data.scoring  format  com.example.habits9.data.scoring  listOf  com.example.habits9.data.scoring  
mutableListOf  com.example.habits9.data.scoring  until  com.example.habits9.data.scoring  getOverallScore 3com.example.habits9.data.scoring.HabitScoringEngine  
Completion 4com.example.habits9.data.scoring.ScoringVerification  Habit 4com.example.habits9.data.scoring.ScoringVerification  HabitScoringEngine 4com.example.habits9.data.scoring.ScoringVerification  	LocalDate 4com.example.habits9.data.scoring.ScoringVerification  Math 4com.example.habits9.data.scoring.ScoringVerification  NumericalHabitType 4com.example.habits9.data.scoring.ScoringVerification  String 4com.example.habits9.data.scoring.ScoringVerification  
StringBuilder 4com.example.habits9.data.scoring.ScoringVerification  ZoneId 4com.example.habits9.data.scoring.ScoringVerification  
appendLine 4com.example.habits9.data.scoring.ScoringVerification  createCompletion 4com.example.habits9.data.scoring.ScoringVerification  createMeasurableHabit 4com.example.habits9.data.scoring.ScoringVerification  createYesNoCompletions 4com.example.habits9.data.scoring.ScoringVerification  createYesNoHabit 4com.example.habits9.data.scoring.ScoringVerification  downTo 4com.example.habits9.data.scoring.ScoringVerification  format 4com.example.habits9.data.scoring.ScoringVerification  listOf 4com.example.habits9.data.scoring.ScoringVerification  
mutableListOf 4com.example.habits9.data.scoring.ScoringVerification  
scoringEngine 4com.example.habits9.data.scoring.ScoringVerification  until 4com.example.habits9.data.scoring.ScoringVerification  verifyMeasurableHabit 4com.example.habits9.data.scoring.ScoringVerification  verifyOverallWeightedScore 4com.example.habits9.data.scoring.ScoringVerification  verifyYesNoHabit 4com.example.habits9.data.scoring.ScoringVerification  
Appendable 	java.lang  
StringBuilder 	java.lang  abs java.lang.Math  
appendLine java.lang.StringBuilder  toString java.lang.StringBuilder  minus 
kotlin.Double  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  LongProgression 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  downTo 
kotlin.ranges  
appendLine kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                