package com.example.habits9.data.scoring

import com.example.habits9.data.Completion
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import com.example.habits9.data.analytics.TimePeriod
import com.example.habits9.utils.HabitScheduler
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject
import javax.inject.Singleton

/**
 * New Habit Scoring Engine implementing the specifications from 1_prompt.md
 * 
 * This is the single source of truth for all habit performance metrics.
 * Replaces all previous scoring systems with a unified, weighted approach.
 * 
 * Key Features:
 * - Overall Score: Weighted average of last 30 days (60%) and all-time (40%)
 * - Timeframe Scores: Consistent calculation across Day, Week, Month, Quarter, Year
 * - Proper handling of Yes/No and Measurable habits
 * - Daily Score capping at 1.0 for measurable habits (no extra credit)
 */
@Singleton
class HabitScoringEngine @Inject constructor() {

    /**
     * Calculate the overall habit score using weighted average.
     * Formula: Overall Score = (Score_Last30Days * 0.6) + (Score_AllTime * 0.4)
     * 
     * @param habitId The habit ID
     * @param habit The habit object
     * @param completions List of all completions for the habit
     * @return Overall score as percentage (0-100)
     */
    fun getOverallScore(
        habitId: Long,
        habit: Habit,
        completions: List<Completion>
    ): Double {
        val today = LocalDate.now()
        
        // Calculate Score_Last30Days
        val last30DaysStart = today.minusDays(29) // Include today, so 30 days total
        val scoreLast30Days = getScoreForPeriod(
            habit = habit,
            completions = completions,
            periodStart = last30DaysStart,
            periodEnd = today
        )
        
        // Calculate Score_AllTime (from habit creation to today)
        val creationDate = LocalDate.ofEpochDay(habit.creationDate / (24 * 60 * 60 * 1000L))
        val scoreAllTime = getScoreForPeriod(
            habit = habit,
            completions = completions,
            periodStart = creationDate,
            periodEnd = today
        )
        
        // Apply weighted formula: (Score_Last30Days * 0.6) + (Score_AllTime * 0.4)
        return (scoreLast30Days * 0.6) + (scoreAllTime * 0.4)
    }

    /**
     * Calculate score for a specific time period.
     * Formula: Score = (Sum of Daily Scores in Period / Number of Scheduled Days in Period) * 100
     * 
     * @param habit The habit object
     * @param completions List of completions for the habit
     * @param timePeriod The time period (Day, Week, Month, Quarter, Year)
     * @return Score as percentage (0-100)
     */
    fun getScoreForPeriod(
        habit: Habit,
        completions: List<Completion>,
        timePeriod: TimePeriod
    ): Double {
        val today = LocalDate.now()
        val (periodStart, periodEnd) = when (timePeriod) {
            TimePeriod.DAY -> Pair(today, today)
            TimePeriod.WEEK -> {
                val startOfWeek = today.minusDays(today.dayOfWeek.value - 1L)
                Pair(startOfWeek, startOfWeek.plusDays(6))
            }
            TimePeriod.MONTH -> {
                val startOfMonth = today.withDayOfMonth(1)
                Pair(startOfMonth, startOfMonth.plusMonths(1).minusDays(1))
            }
            TimePeriod.QUARTER -> {
                val currentQuarter = (today.monthValue - 1) / 3
                val startOfQuarter = today.withMonth(currentQuarter * 3 + 1).withDayOfMonth(1)
                Pair(startOfQuarter, startOfQuarter.plusMonths(3).minusDays(1))
            }
            TimePeriod.YEAR -> {
                val startOfYear = today.withDayOfYear(1)
                Pair(startOfYear, startOfYear.plusYears(1).minusDays(1))
            }
        }
        
        return getScoreForPeriod(habit, completions, periodStart, periodEnd)
    }

    /**
     * Calculate score for a custom date range.
     * Formula: Score = (Sum of Daily Scores in Period / Number of Scheduled Days in Period) * 100
     * 
     * @param habit The habit object
     * @param completions List of completions for the habit
     * @param periodStart Start date (inclusive)
     * @param periodEnd End date (inclusive)
     * @return Score as percentage (0-100)
     */
    fun getScoreForPeriod(
        habit: Habit,
        completions: List<Completion>,
        periodStart: LocalDate,
        periodEnd: LocalDate
    ): Double {
        var totalDailyScores = 0.0
        var totalScheduledDays = 0
        var currentDate = periodStart
        
        // Create a map for quick completion lookup
        val completionMap = completions.associateBy { completion ->
            val dayStart = (completion.timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
            dayStart
        }
        
        while (!currentDate.isAfter(periodEnd)) {
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)
            
            if (isScheduled) {
                totalScheduledDays++
                
                // Calculate daily score for this day
                val dailyScore = calculateDailyScore(habit, completionMap, currentDate)
                totalDailyScores += dailyScore
            }
            
            currentDate = currentDate.plusDays(1)
        }
        
        return if (totalScheduledDays > 0) {
            (totalDailyScores / totalScheduledDays) * 100.0
        } else {
            0.0
        }
    }

    /**
     * Calculate the daily score for a specific day.
     * 
     * For Yes/No habits:
     * - Completed = 1.0
     * - Missed = 0.0
     * 
     * For Measurable habits:
     * - Daily Score = min(1.0, Amount Logged / Goal Amount)
     * - CRITICAL: Score is capped at 1.0 (no extra credit)
     * 
     * @param habit The habit object
     * @param completionMap Map of day timestamps to completions
     * @param date The date to calculate score for
     * @return Daily score (0.0 to 1.0)
     */
    private fun calculateDailyScore(
        habit: Habit,
        completionMap: Map<Long, Completion>,
        date: LocalDate
    ): Double {
        val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
        val completion = completionMap[dayStart]
        
        return when (habit.habitType) {
            HabitType.YES_NO -> {
                // Yes/No habits: 1.0 if completion exists, 0.0 if missed
                if (completion != null) 1.0 else 0.0
            }
            HabitType.NUMERICAL -> {
                // Measurable habits: ratio of logged amount to goal, capped at 1.0
                if (completion?.value.isNullOrEmpty()) {
                    0.0
                } else {
                    val loggedAmount = completion.value?.toDoubleOrNull() ?: 0.0
                    val goalAmount = habit.targetValue

                    if (goalAmount <= 0) {
                        // If no goal is set, treat any logged value as complete
                        if (loggedAmount > 0) 1.0 else 0.0
                    } else {
                        when (habit.numericalHabitType) {
                            NumericalHabitType.AT_LEAST -> {
                                // For AT_LEAST: score = min(1.0, logged / goal)
                                kotlin.math.min(1.0, loggedAmount / goalAmount)
                            }
                            NumericalHabitType.AT_MOST -> {
                                // For AT_MOST: score = 1.0 if logged <= goal, 0.0 otherwise
                                if (loggedAmount <= goalAmount) 1.0 else 0.0
                            }
                        }
                    }
                }
            }
        }
    }
}
