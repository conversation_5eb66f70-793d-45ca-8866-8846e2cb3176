package com.example.habits9.ui;

import com.example.habits9.data.CompletionRepository;
import com.example.habits9.data.HabitRepository;
import com.example.habits9.data.HabitSectionRepository;
import com.example.habits9.data.UserPreferencesRepository;
import com.example.habits9.data.scoring.HabitScoringEngine;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<CompletionRepository> completionRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<HabitSectionRepository> habitSectionRepositoryProvider;

  private final Provider<HabitScoringEngine> scoringEngineProvider;

  public MainViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<HabitSectionRepository> habitSectionRepositoryProvider,
      Provider<HabitScoringEngine> scoringEngineProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.completionRepositoryProvider = completionRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.habitSectionRepositoryProvider = habitSectionRepositoryProvider;
    this.scoringEngineProvider = scoringEngineProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(habitRepositoryProvider.get(), completionRepositoryProvider.get(), userPreferencesRepositoryProvider.get(), habitSectionRepositoryProvider.get(), scoringEngineProvider.get());
  }

  public static MainViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<HabitSectionRepository> habitSectionRepositoryProvider,
      Provider<HabitScoringEngine> scoringEngineProvider) {
    return new MainViewModel_Factory(habitRepositoryProvider, completionRepositoryProvider, userPreferencesRepositoryProvider, habitSectionRepositoryProvider, scoringEngineProvider);
  }

  public static MainViewModel newInstance(HabitRepository habitRepository,
      CompletionRepository completionRepository,
      UserPreferencesRepository userPreferencesRepository,
      HabitSectionRepository habitSectionRepository, HabitScoringEngine scoringEngine) {
    return new MainViewModel(habitRepository, completionRepository, userPreferencesRepository, habitSectionRepository, scoringEngine);
  }
}
