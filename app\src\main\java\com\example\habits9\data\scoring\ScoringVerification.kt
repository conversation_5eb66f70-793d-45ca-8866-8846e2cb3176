package com.example.habits9.data.scoring

import com.example.habits9.data.Completion
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import com.example.habits9.data.FrequencyType
import com.example.habits9.data.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId

/**
 * Manual verification of the new scoring system according to 1_prompt.md specifications
 * This class provides methods to verify all the test cases mentioned in the prompt
 */
object ScoringVerification {

    private val scoringEngine = HabitScoringEngine()

    /**
     * Run all verification tests as specified in the prompt
     */
    fun runAllVerifications(): String {
        val results = StringBuilder()
        results.appendLine("=== HABIT SCORING ENGINE VERIFICATION ===")
        results.appendLine()

        // Verification 1: Yes/No Habit
        results.appendLine("1. YES/NO HABIT VERIFICATION:")
        results.appendLine(verifyYesNoHabit())
        results.appendLine()

        // Verification 2: Measurable Habit
        results.appendLine("2. MEASURABLE HABIT VERIFICATION:")
        results.appendLine(verifyMeasurableHabit())
        results.appendLine()

        // Verification 3: Overall Weighted Score
        results.appendLine("3. OVERALL WEIGHTED SCORE VERIFICATION:")
        results.appendLine(verifyOverallWeightedScore())
        results.appendLine()

        return results.toString()
    }

    /**
     * Verification 1: "Yes/No" Habit Verification
     * - Create a "Yes/No" habit scheduled for every day
     * - For the last 30 days, complete it 15 times. Verify getScoreForPeriod for the Month returns 50
     * - For the last 7 days, complete it 7 times. Verify getScoreForPeriod for the Week returns 100
     */
    private fun verifyYesNoHabit(): String {
        val results = StringBuilder()
        
        // Create a Yes/No habit scheduled for every day
        val habit = createYesNoHabit()
        
        // Test 1: 15 completions out of 30 days = 50%
        val completions30Days = createYesNoCompletions(15, 30)
        val today = LocalDate.now()
        val monthStart = today.minusDays(29) // 30 days total including today
        val monthScore = scoringEngine.getScoreForPeriod(habit, completions30Days, monthStart, today)
        
        results.appendLine("   Monthly Score Test (15/30 days):")
        results.appendLine("   Expected: 50.0%")
        results.appendLine("   Actual: ${String.format("%.1f", monthScore)}%")
        results.appendLine("   Result: ${if (Math.abs(monthScore - 50.0) < 0.1) "✓ PASS" else "✗ FAIL"}")
        results.appendLine()
        
        // Test 2: 7 completions out of 7 days = 100%
        val completions7Days = createYesNoCompletions(7, 7)
        val weekStart = today.minusDays(6) // 7 days total including today
        val weekScore = scoringEngine.getScoreForPeriod(habit, completions7Days, weekStart, today)
        
        results.appendLine("   Weekly Score Test (7/7 days):")
        results.appendLine("   Expected: 100.0%")
        results.appendLine("   Actual: ${String.format("%.1f", weekScore)}%")
        results.appendLine("   Result: ${if (Math.abs(weekScore - 100.0) < 0.1) "✓ PASS" else "✗ FAIL"}")
        
        return results.toString()
    }

    /**
     * Verification 2: "Measurable" Habit Verification
     * - Create a "Measurable" habit with a goal of "Read 20 pages"
     * - Day 1: log 10 pages (Daily Score = 0.5)
     * - Day 2: log 20 pages (Daily Score = 1.0)
     * - Day 3: log 30 pages (Daily Score = 1.0, because it's capped)
     * - Day 4: scheduled but logged 0 pages (Daily Score = 0)
     * - Total score should be (0.5 + 1.0 + 1.0 + 0) = 2.5
     * - Result should be (2.5 / 4) * 100 = 62.5%
     */
    private fun verifyMeasurableHabit(): String {
        val results = StringBuilder()
        
        // Create a measurable habit with goal of 20 pages
        val habit = createMeasurableHabit(targetValue = 20.0, unit = "pages")
        
        val today = LocalDate.now()
        val completions = listOf(
            // Day 1: 10 pages (Daily Score = 0.5)
            createCompletion(today.minusDays(3), "10"),
            // Day 2: 20 pages (Daily Score = 1.0)
            createCompletion(today.minusDays(2), "20"),
            // Day 3: 30 pages (Daily Score = 1.0, capped)
            createCompletion(today.minusDays(1), "30"),
            // Day 4: 0 pages (Daily Score = 0.0)
            createCompletion(today, "0")
        )
        
        // Test 4-day period score
        val periodStart = today.minusDays(3)
        val periodEnd = today
        val score = scoringEngine.getScoreForPeriod(habit, completions, periodStart, periodEnd)
        
        results.appendLine("   Measurable Habit Test (20 pages goal):")
        results.appendLine("   Day 1: 10 pages → Daily Score = 0.5")
        results.appendLine("   Day 2: 20 pages → Daily Score = 1.0")
        results.appendLine("   Day 3: 30 pages → Daily Score = 1.0 (capped)")
        results.appendLine("   Day 4: 0 pages → Daily Score = 0.0")
        results.appendLine("   Expected: (0.5 + 1.0 + 1.0 + 0.0) / 4 * 100 = 62.5%")
        results.appendLine("   Actual: ${String.format("%.1f", score)}%")
        results.appendLine("   Result: ${if (Math.abs(score - 62.5) < 0.1) "✓ PASS" else "✗ FAIL"}")
        
        return results.toString()
    }

    /**
     * Verification 3: Overall Weighted Score Verification
     * - Create a new habit and complete it perfectly for 30 days
     * - Score_Last30Days = 100, Score_AllTime = 100
     * - Verify getOverallScore returns 100: (100 * 0.6) + (100 * 0.4) = 100
     * - Simulate next 30 days with 50% completion
     * - New Score_Last30Days = 50, Score_AllTime = 75 (average of 60 days)
     * - Verify new getOverallScore returns 60: (50 * 0.6) + (75 * 0.4) = 60
     */
    private fun verifyOverallWeightedScore(): String {
        val results = StringBuilder()
        
        val habit = createYesNoHabit()
        
        // Phase 1: Perfect completion for 30 days
        val perfectCompletions = createYesNoCompletions(30, 30)
        val overallScore1 = scoringEngine.getOverallScore(1L, habit, perfectCompletions)
        
        results.appendLine("   Phase 1 - Perfect Completion (30/30 days):")
        results.appendLine("   Expected: 100.0%")
        results.appendLine("   Actual: ${String.format("%.1f", overallScore1)}%")
        results.appendLine("   Result: ${if (Math.abs(overallScore1 - 100.0) < 0.1) "✓ PASS" else "✗ FAIL"}")
        results.appendLine()
        
        // Phase 2: Add 30 more days with 50% completion (15 out of 30)
        val today = LocalDate.now()
        val mixedCompletions = mutableListOf<Completion>()
        
        // First 30 days: perfect (100%)
        for (i in 59 downTo 30) {
            mixedCompletions.add(createCompletion(today.minusDays(i.toLong()), "completed"))
        }
        
        // Last 30 days: 50% completion (15 out of 30)
        for (i in 29 downTo 0) {
            if (i % 2 == 0) { // Complete every other day = 15 completions
                mixedCompletions.add(createCompletion(today.minusDays(i.toLong()), "completed"))
            }
        }
        
        val overallScore2 = scoringEngine.getOverallScore(1L, habit, mixedCompletions)
        
        results.appendLine("   Phase 2 - Mixed Completion (45/60 days total):")
        results.appendLine("   Last 30 days: 15/30 = 50%")
        results.appendLine("   All time: 45/60 = 75%")
        results.appendLine("   Expected: (50 * 0.6) + (75 * 0.4) = 60.0%")
        results.appendLine("   Actual: ${String.format("%.1f", overallScore2)}%")
        results.appendLine("   Result: ${if (Math.abs(overallScore2 - 60.0) < 0.1) "✓ PASS" else "✗ FAIL"}")
        
        return results.toString()
    }

    // Helper methods for creating test data

    private fun createYesNoHabit(): Habit {
        return Habit(
            id = 1L,
            name = "Test Yes/No Habit",
            description = "Test habit for verification",
            creationDate = LocalDate.now().minusDays(60).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            type = "YES_NO",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 2L, 3L, 4L, 5L, 6L, 7L), // All days of week
            targetValue = 1.0,
            targetType = NumericalHabitType.AT_LEAST.value,
            unit = ""
        )
    }

    private fun createMeasurableHabit(targetValue: Double, unit: String): Habit {
        return Habit(
            id = 1L,
            name = "Test Measurable Habit",
            description = "Test measurable habit for verification",
            creationDate = LocalDate.now().minusDays(60).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            type = "NUMERICAL",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 2L, 3L, 4L, 5L, 6L, 7L), // All days of week
            targetValue = targetValue,
            targetType = NumericalHabitType.AT_LEAST.value,
            unit = unit
        )
    }

    private fun createYesNoCompletions(completedDays: Int, totalDays: Int): List<Completion> {
        val today = LocalDate.now()
        val completions = mutableListOf<Completion>()
        
        for (i in 0 until completedDays) {
            completions.add(createCompletion(today.minusDays(i.toLong()), "completed"))
        }
        
        return completions
    }

    private fun createCompletion(date: LocalDate, value: String): Completion {
        val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        return Completion(
            id = "test-${timestamp}",
            habitId = 1L,
            timestamp = timestamp,
            value = if (value == "completed") null else value
        )
    }
}
