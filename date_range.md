# Date Range for Habit Views

This document summarizes the supported date ranges for a habit across the main UI views and internal computations.

## Key Takeaways
- History view shows from the earliest known entry date up to today. It does not display future dates.
- Score card also shows scores from the earliest known date up to today.
- Internally, score and streak calculations are computed up to 30 days into the future (today + 30), but the UI does not display those future dates.

## Source Pointers
- History card presenter (range and series):
  - `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/ui/screens/habits/show/views/HistoryCard.kt`
    - `buildState`: determines `oldest` and uses `getByInterval(oldest, today)`
- Score card presenter (range and aggregation):
  - `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/ui/screens/habits/show/views/ScoreCard.kt`
    - `buildState`: similar logic using `scores.getByInterval(oldest, today)`
- Habit recomputation range (internal):
  - `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/Habit.kt`
    - `recompute`: computes scores and streaks until `to = today.plus(30)`

## History View Range
- Start (oldest date): the timestamp of the oldest known computed entry for the habit.
  - Obtained via `habit.computedEntries.getKnown().lastOrNull()?.timestamp`.
  - If no entries exist yet, this falls back to `today`.
- End (newest date): `today` (`DateUtils.getTodayWithOffset()`).
- Data used: `computedEntries.getByInterval(oldest, today)`.
- Future days are not included in the history series.

### Example (today = 11 Aug 2025)
- Brand-new habit with no entries:
  - Oldest: 11 Aug 2025
  - Newest: 11 Aug 2025
- Habit with backdated entries (e.g., first entry on 01 Jan 2025):
  - Oldest: 01 Jan 2025
  - Newest: 11 Aug 2025

## Score Card Range
- Uses the same `oldest .. today` interval for building the list of score values that are then bucket-averaged (day/week/month/quarter/year).
- Thus, the score chart does not display future days.

## Internal Future Computations
- During `Habit.recompute`, the app sets `to = today.plus(30)` and recomputes:
  - Scores: `scores.recompute(..., from = oldestOrToday, to = today.plus(30))`
  - Streaks: similarly recomputed through `today.plus(30)`
- These forward-looking values are used for consistency in derived metrics, but they are not shown in the History or Score card displays, which cap at `today`.

## Notes
- `computedEntries.getKnown()` returns all known entries sorted newest → oldest. The last element is the oldest known date.
- For boolean habits, `computedEntries` includes frequency-driven `YES_AUTO` days in addition to manual entries; for numerical habits, entries are copied as-is.
- If you add backdated entries, the history range extends automatically to include the earliest such date.
