package com.example.habits9.di;

import com.example.habits9.data.HabitSectionRepository;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitSectionRepositoryFactory implements Factory<HabitSectionRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<FirebaseAuth> authProvider;

  public DatabaseModule_ProvideHabitSectionRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider, Provider<FirebaseAuth> authProvider) {
    this.firestoreProvider = firestoreProvider;
    this.authProvider = authProvider;
  }

  @Override
  public HabitSectionRepository get() {
    return provideHabitSectionRepository(firestoreProvider.get(), authProvider.get());
  }

  public static DatabaseModule_ProvideHabitSectionRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider, Provider<FirebaseAuth> authProvider) {
    return new DatabaseModule_ProvideHabitSectionRepositoryFactory(firestoreProvider, authProvider);
  }

  public static HabitSectionRepository provideHabitSectionRepository(FirebaseFirestore firestore,
      FirebaseAuth auth) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitSectionRepository(firestore, auth));
  }
}
