/ Header Record For PersistentHashMapValueStoragei hE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\CompletionRepository.ktZ YE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\Habit.ktd cE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitRepository.ktk jE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitSectionRepository.ktn mE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\UserPreferencesRepository.ktw vE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\analytics\HabitAnalyticsRepository.ktt sE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\analytics\HabitAnalyticsUseCase.kto nE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\scoring\HabitScoringEngine.kta `E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\di\DatabaseModule.kt` _E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\MainViewModel.kte dE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\auth\AuthViewModel.kts rE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\createhabit\CreateHabitViewModel.ktp oE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\details\HabitDetailsViewModel.ktu tE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\habitreorder\HabitReorderViewModel.kty xE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\managesections\ManageSectionsViewModel.ktm lE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\settings\SettingsViewModel.kt_ ^E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\uhabits_99\MainActivity.kt