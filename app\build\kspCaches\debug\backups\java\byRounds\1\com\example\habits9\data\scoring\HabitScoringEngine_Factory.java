package com.example.habits9.data.scoring;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitScoringEngine_Factory implements Factory<HabitScoringEngine> {
  @Override
  public HabitScoringEngine get() {
    return newInstance();
  }

  public static HabitScoringEngine_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HabitScoringEngine newInstance() {
    return new HabitScoringEngine();
  }

  private static final class InstanceHolder {
    private static final HabitScoringEngine_Factory INSTANCE = new HabitScoringEngine_Factory();
  }
}
