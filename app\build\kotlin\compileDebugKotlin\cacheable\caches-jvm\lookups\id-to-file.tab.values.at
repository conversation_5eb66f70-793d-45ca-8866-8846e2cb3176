/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/example/habits9/HabitsApplication.kt9 8app/src/main/java/com/example/habits9/data/Completion.ktC Bapp/src/main/java/com/example/habits9/data/CompletionRepository.kt> =app/src/main/java/com/example/habits9/data/FrequencyModels.kt4 3app/src/main/java/com/example/habits9/data/Habit.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.kt; :app/src/main/java/com/example/habits9/data/HabitSection.ktE Dapp/src/main/java/com/example/habits9/data/HabitSectionRepository.kt8 7app/src/main/java/com/example/habits9/data/HabitType.ktH Gapp/src/main/java/com/example/habits9/data/UserPreferencesRepository.ktF Eapp/src/main/java/com/example/habits9/data/analytics/AnalyticsDemo.ktN Mapp/src/main/java/com/example/habits9/data/analytics/AnalyticsVerification.ktG Fapp/src/main/java/com/example/habits9/data/analytics/HabitAnalytics.ktQ Papp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsRepository.ktN Mapp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsUseCase.ktL Kapp/src/main/java/com/example/habits9/data/firestore/FirestoreCompletion.ktL Kapp/src/main/java/com/example/habits9/data/firestore/FirestoreConverters.ktG Fapp/src/main/java/com/example/habits9/data/firestore/FirestoreHabit.ktN Mapp/src/main/java/com/example/habits9/data/firestore/FirestoreHabitSection.kt; :app/src/main/java/com/example/habits9/di/DatabaseModule.kt: 9app/src/main/java/com/example/habits9/ui/MainViewModel.kt< ;app/src/main/java/com/example/habits9/ui/auth/AuthScreen.kt? >app/src/main/java/com/example/habits9/ui/auth/AuthViewModel.ktD Capp/src/main/java/com/example/habits9/ui/auth/VerificationScreen.ktU Tapp/src/main/java/com/example/habits9/ui/components/EnhancedFrequencyPickerDialog.ktL Kapp/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt@ ?app/src/main/java/com/example/habits9/ui/components/SortMenu.ktM Lapp/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt^ ]app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.ktT Sapp/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.ktL Kapp/src/main/java/com/example/habits9/ui/habitreorder/HabitReorderScreen.ktO Napp/src/main/java/com/example/habits9/ui/habitreorder/HabitReorderViewModel.ktX Wapp/src/main/java/com/example/habits9/ui/habittypeselection/HabitTypeSelectionScreen.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/ui/managesections/ManageSectionsScreen.ktS Rapp/src/main/java/com/example/habits9/ui/managesections/ManageSectionsViewModel.ktD Capp/src/main/java/com/example/habits9/ui/settings/SettingsScreen.ktG Fapp/src/main/java/com/example/habits9/ui/settings/SettingsViewModel.kt> =app/src/main/java/com/example/habits9/utils/HabitScheduler.kt9 8app/src/main/java/com/example/uhabits_99/MainActivity.kt; :app/src/main/java/com/example/uhabits_99/ui/theme/Color.kt; :app/src/main/java/com/example/uhabits_99/ui/theme/Theme.kt: 9app/src/main/java/com/example/uhabits_99/ui/theme/Type.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.kt9 8app/src/main/java/com/example/uhabits_99/MainActivity.ktC Bapp/src/main/java/com/example/habits9/data/CompletionRepository.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.ktE Dapp/src/main/java/com/example/habits9/data/HabitSectionRepository.ktC Bapp/src/main/java/com/example/habits9/data/CompletionRepository.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.ktE Dapp/src/main/java/com/example/habits9/data/HabitSectionRepository.ktC Bapp/src/main/java/com/example/habits9/data/CompletionRepository.ktM Lapp/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt^ ]app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.ktT Sapp/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.kt9 8app/src/main/java/com/example/uhabits_99/MainActivity.kt> =app/src/main/java/com/example/habits9/data/HabitRepository.ktM Lapp/src/main/java/com/example/habits9/ui/createhabit/CreateHabitViewModel.kt^ ]app/src/main/java/com/example/habits9/ui/createmeasurablehabit/CreateMeasurableHabitScreen.ktT Sapp/src/main/java/com/example/habits9/ui/createyesnohabit/CreateYesNoHabitScreen.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.ktY Xapp/src/main/java/com/example/habits9/ui/details/components/CompletionCalendarHeatmap.ktV Uapp/src/main/java/com/example/habits9/ui/details/components/CompletionHistoryChart.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktV Uapp/src/main/java/com/example/habits9/ui/details/components/CompletionHistoryChart.ktG Fapp/src/main/java/com/example/habits9/data/analytics/HabitAnalytics.ktQ Papp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsRepository.ktN Mapp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsUseCase.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.ktJ Iapp/src/main/java/com/example/habits9/ui/details/components/ScoreChart.ktF Eapp/src/main/java/com/example/habits9/data/analytics/AnalyticsDemo.ktJ Iapp/src/main/java/com/example/habits9/ui/details/components/ScoreChart.ktJ Iapp/src/main/java/com/example/habits9/ui/details/components/ScoreChart.ktJ Iapp/src/main/java/com/example/habits9/ui/details/components/ScoreChart.ktJ Iapp/src/main/java/com/example/habits9/ui/details/components/ScoreChart.ktG Fapp/src/main/java/com/example/habits9/data/analytics/HabitAnalytics.ktN Mapp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsUseCase.ktG Fapp/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.ktR Qapp/src/main/java/com/example/habits9/ui/details/components/PerformanceHeatmap.ktF Eapp/src/main/java/com/example/habits9/data/analytics/AnalyticsDemo.ktQ Papp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsRepository.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.ktV Uapp/src/main/java/com/example/habits9/ui/details/components/CompletionHistoryChart.ktN Mapp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsUseCase.ktI Happ/src/main/java/com/example/habits9/data/scoring/HabitScoringEngine.kt: 9app/src/main/java/com/example/habits9/ui/MainViewModel.ktQ Papp/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsRepository.ktJ Iapp/src/main/java/com/example/habits9/ui/details/HabitDetailsViewModel.kt< ;app/src/main/java/com/example/habits9/ui/home/<USER>/src/main/java/com/example/habits9/data/analytics/HabitAnalyticsUseCase.ktJ Iapp/src/main/java/com/example/habits9/data/scoring/ScoringVerification.kt