package com.example.habits9.data.scoring

import com.example.habits9.data.Completion
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import com.example.habits9.data.FrequencyType
import com.example.habits9.data.DayOfWeek
import com.example.habits9.data.analytics.TimePeriod
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import java.time.LocalDate
import java.time.ZoneId

/**
 * Comprehensive verification tests for the new HabitScoringEngine
 * Implements all verification scenarios from 1_prompt.md
 */
class HabitScoringEngineTest {

    private lateinit var scoringEngine: HabitScoringEngine

    @Before
    fun setup() {
        scoringEngine = HabitScoringEngine()
    }

    /**
     * Verification 1: "Yes/No" Habit Verification
     * - Create a "Yes/No" habit scheduled for every day
     * - For the last 30 days, complete it 15 times. Verify getScoreForPeriod for the Month returns 50
     * - For the last 7 days, complete it 7 times. Verify getScoreForPeriod for the Week returns 100
     */
    @Test
    fun testYesNoHabitVerification() {
        // Create a Yes/No habit scheduled for every day
        val habit = createYesNoHabit()
        
        // Create completions for 15 out of 30 days
        val completions = createYesNoCompletions(15, 30)
        
        // Test monthly score (should be 50%)
        val today = LocalDate.now()
        val monthStart = today.minusDays(29) // 30 days total including today
        val monthScore = scoringEngine.getScoreForPeriod(habit, completions, monthStart, today)
        assertEquals("Monthly score should be 50%", 50.0, monthScore, 0.1)
        
        // Create completions for all 7 days of the week
        val weekCompletions = createYesNoCompletions(7, 7)
        
        // Test weekly score (should be 100%)
        val weekStart = today.minusDays(6) // 7 days total including today
        val weekScore = scoringEngine.getScoreForPeriod(habit, weekCompletions, weekStart, today)
        assertEquals("Weekly score should be 100%", 100.0, weekScore, 0.1)
    }

    /**
     * Verification 2: "Measurable" Habit Verification
     * - Create a "Measurable" habit with a goal of "Read 20 pages"
     * - Day 1: log 10 pages (Daily Score = 0.5)
     * - Day 2: log 20 pages (Daily Score = 1.0)
     * - Day 3: log 30 pages (Daily Score = 1.0, because it's capped)
     * - Day 4: scheduled but logged 0 pages (Daily Score = 0)
     * - Total score should be (0.5 + 1.0 + 1.0 + 0) = 2.5
     * - Result should be (2.5 / 4) * 100 = 62.5%
     */
    @Test
    fun testMeasurableHabitVerification() {
        // Create a measurable habit with goal of 20 pages
        val habit = createMeasurableHabit(targetValue = 20.0, unit = "pages")
        
        val today = LocalDate.now()
        val completions = listOf(
            // Day 1: 10 pages (Daily Score = 0.5)
            createCompletion(today.minusDays(3), "10"),
            // Day 2: 20 pages (Daily Score = 1.0)
            createCompletion(today.minusDays(2), "20"),
            // Day 3: 30 pages (Daily Score = 1.0, capped)
            createCompletion(today.minusDays(1), "30"),
            // Day 4: 0 pages (Daily Score = 0.0)
            createCompletion(today, "0")
        )
        
        // Test 4-day period score
        val periodStart = today.minusDays(3)
        val periodEnd = today
        val score = scoringEngine.getScoreForPeriod(habit, completions, periodStart, periodEnd)
        
        // Expected: (0.5 + 1.0 + 1.0 + 0.0) / 4 * 100 = 62.5%
        assertEquals("Measurable habit score should be 62.5%", 62.5, score, 0.1)
    }

    /**
     * Verification 3: Overall Weighted Score Verification
     * - Create a new habit and complete it perfectly for 30 days
     * - Score_Last30Days = 100, Score_AllTime = 100
     * - Verify getOverallScore returns 100: (100 * 0.6) + (100 * 0.4) = 100
     * - Simulate next 30 days with 50% completion
     * - New Score_Last30Days = 50, Score_AllTime = 75 (average of 60 days)
     * - Verify new getOverallScore returns 60: (50 * 0.6) + (75 * 0.4) = 60
     */
    @Test
    fun testOverallWeightedScoreVerification() {
        val habit = createYesNoHabit()
        
        // Phase 1: Perfect completion for 30 days
        val perfectCompletions = createYesNoCompletions(30, 30)
        val overallScore1 = scoringEngine.getOverallScore(1L, habit, perfectCompletions)
        assertEquals("Perfect habit should score 100%", 100.0, overallScore1, 0.1)
        
        // Phase 2: Add 30 more days with 50% completion (15 out of 30)
        val today = LocalDate.now()
        val mixedCompletions = mutableListOf<Completion>()
        
        // First 30 days: perfect (100%)
        for (i in 59 downTo 30) {
            mixedCompletions.add(createCompletion(today.minusDays(i.toLong()), "true"))
        }
        
        // Last 30 days: 50% completion (15 out of 30)
        for (i in 29 downTo 0) {
            if (i % 2 == 0) { // Complete every other day = 15 completions
                mixedCompletions.add(createCompletion(today.minusDays(i.toLong()), "true"))
            }
        }
        
        val overallScore2 = scoringEngine.getOverallScore(1L, habit, mixedCompletions)
        
        // Expected calculation:
        // Score_Last30Days = 50% (15 out of 30)
        // Score_AllTime = 75% (45 out of 60)
        // Overall = (50 * 0.6) + (75 * 0.4) = 30 + 30 = 60%
        assertEquals("Mixed completion habit should score 60%", 60.0, overallScore2, 0.1)
    }

    /**
     * Test daily score capping for measurable habits
     * Ensures that exceeding the goal doesn't give extra credit
     */
    @Test
    fun testDailyScoreCapping() {
        val habit = createMeasurableHabit(targetValue = 10.0, unit = "km")
        val today = LocalDate.now()
        
        // Test various amounts
        val testCases = listOf(
            Pair("5", 0.5),   // Half goal = 0.5 score
            Pair("10", 1.0),  // Exact goal = 1.0 score
            Pair("15", 1.0),  // Exceed goal = 1.0 score (capped)
            Pair("20", 1.0),  // Double goal = 1.0 score (capped)
            Pair("0", 0.0)    // No completion = 0.0 score
        )
        
        testCases.forEachIndexed { index, (amount, expectedScore) ->
            val completion = listOf(createCompletion(today.minusDays(index.toLong()), amount))
            val score = scoringEngine.getScoreForPeriod(
                habit, completion, 
                today.minusDays(index.toLong()), 
                today.minusDays(index.toLong())
            )
            assertEquals(
                "Amount $amount should give score ${expectedScore * 100}%", 
                expectedScore * 100, score, 0.1
            )
        }
    }

    // Helper methods for creating test data

    private fun createYesNoHabit(): Habit {
        return Habit(
            id = 1L,
            name = "Test Yes/No Habit",
            description = "Test habit for verification",
            creationDate = LocalDate.now().minusDays(60).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            habitType = HabitType.YES_NO,
            frequencyType = FrequencyType.DAILY,
            repeatsEvery = 1,
            daysOfWeek = DayOfWeek.values().toList(),
            targetValue = 1.0,
            numericalHabitType = NumericalHabitType.AT_LEAST,
            unit = ""
        )
    }

    private fun createMeasurableHabit(targetValue: Double, unit: String): Habit {
        return Habit(
            id = 1L,
            name = "Test Measurable Habit",
            description = "Test measurable habit for verification",
            creationDate = LocalDate.now().minusDays(60).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            habitType = HabitType.NUMERICAL,
            frequencyType = FrequencyType.DAILY,
            repeatsEvery = 1,
            daysOfWeek = DayOfWeek.values().toList(),
            targetValue = targetValue,
            numericalHabitType = NumericalHabitType.AT_LEAST,
            unit = unit
        )
    }

    private fun createYesNoCompletions(completedDays: Int, totalDays: Int): List<Completion> {
        val today = LocalDate.now()
        val completions = mutableListOf<Completion>()
        
        for (i in 0 until completedDays) {
            completions.add(createCompletion(today.minusDays(i.toLong()), "true"))
        }
        
        return completions
    }

    private fun createCompletion(date: LocalDate, value: String): Completion {
        val timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        return Completion(
            id = "test-${timestamp}",
            habitId = 1L,
            timestamp = timestamp,
            value = if (value == "true") null else value
        )
    }
}
